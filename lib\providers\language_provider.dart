import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageProvider with ChangeNotifier {
  Locale _locale = Locale('en');
  Locale get locale => _locale;
  bool _initialized = false;
  bool get initialized => _initialized;

  LanguageProvider() {
    _loadSavedLanguage();
  }

  Future<void> _loadSavedLanguage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedLanguage = prefs.getString('language_code');

      if (savedLanguage != null) {
        _locale = Locale(savedLanguage);
      }
    } catch (e) {
      print('Error loading saved language: $e');
      // Continue with default language (English)
    } finally {
      _initialized = true;
      notifyListeners();
    }
  }

  void setLocaleDirectly(String languageCode) {
    if (_locale.languageCode != languageCode) {
      _locale = Locale(languageCode);
      notifyListeners();

      // Try to save the preference in the background
      _saveLanguagePreference(languageCode);
    }
  }

  Future<void> _saveLanguagePreference(String languageCode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('language_code', languageCode);
      print('Language preference saved: $languageCode');
    } catch (e) {
      print('Error saving language preference: $e');
      // Continue without saving preference
    }
  }

  Future<void> changeLanguage(String languageCode) async {
    setLocaleDirectly(languageCode);
  }
}
