def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

// Add repositories section here
repositories {
    google() // Google's Maven Repository
    mavenCentral() // Maven Central Repository
}

apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"

android {
    namespace 'com.example.wd_panel'
    compileSdkVersion 34
    ndkVersion flutter.ndkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
        freeCompilerArgs += ["-Xlint:deprecation", "-Xlint:unchecked"]
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        applicationId "com.example.wd_panel"  // Reverted back to original package name
        minSdkVersion 23
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
    }

    buildTypes {
        release {
            signingConfig signingConfigs.debug
            minifyEnabled false  // Disable minification to avoid keystore issues
            shrinkResources false  // Disable resource shrinking
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
            // Apply the same proguard rules for debug builds
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    // Set custom APK output filename
    android.applicationVariants.all { variant ->
        variant.outputs.all {
            outputFileName = "quickk-distributor.apk"
        }
    }

    // Removed duplicate compileOptions and kotlinOptions sections
}

flutter {
    source '../..'
}

dependencies {
    implementation platform('com.google.firebase:firebase-bom:33.4.0')
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation 'com.google.firebase:firebase-analytics:22.1.2'
    implementation 'com.google.firebase:firebase-auth:23.0.0'
    implementation 'com.google.firebase:firebase-firestore:25.1.0'
    implementation 'com.google.firebase:firebase-common:21.0.0'
    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'com.google.firebase:firebase-appcheck-playintegrity'
    implementation 'com.google.android.play:integrity:1.4.0'
}

apply plugin: 'com.google.gms.google-services'

afterEvaluate {
    tasks.matching { task -> task.name.startsWith("map") }.all { task ->
        task.dependsOn 'processDebugGoogleServices'
    }
}
