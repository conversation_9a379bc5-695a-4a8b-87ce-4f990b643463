import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For TextInputFormatter
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class SalesRegisterScreen extends StatefulWidget {
  @override
  _SalesRegisterScreenState createState() => _SalesRegisterScreenState();
}

class _SalesRegisterScreenState extends State<SalesRegisterScreen> {
  bool _isSubmitting = false; // Flag to prevent multiple submission attempts
  bool _hasNonZeroQuantity = false; // Flag to track if any quantity is non-zero
  String? _selectedDsName;
  final _mondAppleQtyController = TextEditingController();
  final _mondMintQtyController = TextEditingController();
  final _mondStrawberryQtyController = TextEditingController();

  List<String?> _dsNames = [];

  @override
  void initState() {
    super.initState();
    _fetchDsNames();

    // Add listeners to quantity controllers to check for non-zero values
    _mondAppleQtyController.addListener(_checkQuantities);
    _mondMintQtyController.addListener(_checkQuantities);
    _mondStrawberryQtyController.addListener(_checkQuantities);
  }

  @override
  void dispose() {
    // Clean up controllers when the widget is disposed
    _mondAppleQtyController.removeListener(_checkQuantities);
    _mondMintQtyController.removeListener(_checkQuantities);
    _mondStrawberryQtyController.removeListener(_checkQuantities);

    _mondAppleQtyController.dispose();
    _mondMintQtyController.dispose();
    _mondStrawberryQtyController.dispose();
    super.dispose();
  }

  // Check if any quantity is non-zero
  void _checkQuantities() {
    final appleQty = int.tryParse(_mondAppleQtyController.text) ?? 0;
    final varianceQty = int.tryParse(_mondMintQtyController.text) ?? 0;
    final strawberryQty = int.tryParse(_mondStrawberryQtyController.text) ?? 0;

    setState(() {
      _hasNonZeroQuantity = appleQty > 0 || varianceQty > 0 || strawberryQty > 0;
    });
  }

  Future<void> _fetchDsNames() async {
    try {
      User? user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        // First try to find the user document by authUid field
        QuerySnapshot userQuery = await FirebaseFirestore.instance
            .collection('wdUsers')
            .where('authUid', isEqualTo: user.uid)
            .limit(1)
            .get();

        // If no document found with authUid, try to find by phone number
        if (userQuery.docs.isEmpty) {
          // Extract phone number from Firebase user
          String? phoneNumber = user.phoneNumber;
          if (phoneNumber != null && phoneNumber.startsWith('+91')) {
            // Remove the +91 prefix to match our stored format
            String mobileNumber = phoneNumber.substring(3);

            userQuery = await FirebaseFirestore.instance
                .collection('wdUsers')
                .where('mobileNumber', isEqualTo: mobileNumber)
                .limit(1)
                .get();
          }
        }

        if (userQuery.docs.isEmpty) {
          print('User document does not exist in database');
          // Handle the case when the document doesn't exist
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('User data not found. Please log out and log in again.'),
          ));
          return;
        }

        // Get the first matching document
        DocumentSnapshot userDoc = userQuery.docs.first;

        // Update the authUid field if it's not set
        if (userDoc['authUid'] == null) {
          await FirebaseFirestore.instance
              .collection('wdUsers')
              .doc(userDoc.id)
              .update({'authUid': user.uid});
        }

        String wdCode = userDoc['wdId'];

        QuerySnapshot querySnapshot = await FirebaseFirestore.instance
            .collection('users')
            .where('wdCode', isEqualTo: wdCode)
            .get();

        setState(() {
          _dsNames = [null]; // Start with a null option
          _dsNames.addAll(querySnapshot.docs.map((doc) {
            String name = doc['name'] as String;
            String dsId = doc['dsId'] as String;
            return '$name\_$dsId';  // Create unique DS Name by combining name and dsId
          }).toList());
          _selectedDsName = null; // Reset selected value when fetching new data
        });
      }
    } catch (e) {
      print('Error fetching DS names: $e');
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Error loading data. Please try again later.'),
      ));
    }
  }

  void _submitSalesRegister() async {
    // Prevent multiple submission attempts
    if (_isSubmitting) return;

    try {
      setState(() {
        _isSubmitting = true; // Set flag to prevent multiple attempts
      });

      User? user = FirebaseAuth.instance.currentUser;
      if (user != null && _selectedDsName != null) {
        // Show confirmation dialog before submitting
        bool confirmed = await _showConfirmationDialog();

        if (!confirmed) {
          setState(() {
            _isSubmitting = false; // Reset flag if user cancels
          });
          return; // User cancelled the submission
        }

        String dsId = _selectedDsName!.split('_').last; // Extract dsId from selected DS Name

        // First try to find the user document by authUid field
        QuerySnapshot userQuery = await FirebaseFirestore.instance
            .collection('wdUsers')
            .where('authUid', isEqualTo: user.uid)
            .limit(1)
            .get();

        // If no document found with authUid, try to find by phone number
        if (userQuery.docs.isEmpty) {
          // Extract phone number from Firebase user
          String? phoneNumber = user.phoneNumber;
          if (phoneNumber != null && phoneNumber.startsWith('+91')) {
            // Remove the +91 prefix to match our stored format
            String mobileNumber = phoneNumber.substring(3);

            userQuery = await FirebaseFirestore.instance
                .collection('wdUsers')
                .where('mobileNumber', isEqualTo: mobileNumber)
                .limit(1)
                .get();
          }
        }

        if (userQuery.docs.isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('User data not found. Please log out and log in again.'),
          ));
          setState(() {
            _isSubmitting = false; // Reset flag if user data not found
          });
          return;
        }

        // Get the first matching document
        DocumentSnapshot userDoc = userQuery.docs.first;

        // Update the authUid field if it's not set
        if (userDoc['authUid'] == null) {
          await FirebaseFirestore.instance
              .collection('wdUsers')
              .doc(userDoc.id)
              .update({'authUid': user.uid});
        }

        String wdId = userDoc['wdId'];

        // Get the DS document to update quantities
        QuerySnapshot dsQuery = await FirebaseFirestore.instance
            .collection('users')
            .where('dsId', isEqualTo: dsId)
            .limit(1)
            .get();

        if (dsQuery.docs.isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('DS not found. Please select a different DS.'),
          ));
          setState(() {
            _isSubmitting = false;
          });
          return;
        }

        DocumentSnapshot dsDoc = dsQuery.docs.first;
        String dsDocId = dsDoc.id;

        // Get current quantities
        int currentAppleQty = (dsDoc.data() as Map<String, dynamic>)['appleQty'] ?? 0;
        int currentVarianceQty = (dsDoc.data() as Map<String, dynamic>)['varianceQty'] ?? 0;
        int currentStrawberryQty = (dsDoc.data() as Map<String, dynamic>)['strawberryQty'] ?? 0;

        // Parse new quantities (with fallback to 0 if parsing fails)
        int newAppleQty = int.tryParse(_mondAppleQtyController.text) ?? 0;
        int newVarianceQty = int.tryParse(_mondMintQtyController.text) ?? 0;
        int newStrawberryQty = int.tryParse(_mondStrawberryQtyController.text) ?? 0;

        // Calculate updated quantities
        int updatedAppleQty = currentAppleQty + newAppleQty;
        int updatedVarianceQty = currentVarianceQty + newVarianceQty;
        int updatedStrawberryQty = currentStrawberryQty + newStrawberryQty;

        // Start a batch write to ensure both operations succeed or fail together
        WriteBatch batch = FirebaseFirestore.instance.batch();

        // Update the DS document with new quantities
        batch.update(FirebaseFirestore.instance.collection('users').doc(dsDocId), {
          'appleQty': updatedAppleQty,
          'varianceQty': updatedVarianceQty,
          'strawberryQty': updatedStrawberryQty,
          'lastUpdated': FieldValue.serverTimestamp(),
        });

        // Add the sales register entry
        DocumentReference salesRegRef = FirebaseFirestore.instance.collection('salesRegister').doc();
        batch.set(salesRegRef, {
          'dsId': dsId,
          'mondApple': newAppleQty, // Store as integer instead of string
          'mondMint': newVarianceQty, // Store as integer instead of string
          'mondStrawberry': newStrawberryQty, // Store as integer instead of string
          'totalQty': _calculateTotalQty(),
          'totalTQty': _calculateTotalTQty(),
          'createdBy': wdId,
          'createdAt': FieldValue.serverTimestamp(),
          'previousAppleQty': currentAppleQty,
          'previousVarianceQty': currentVarianceQty,
          'previousStrawberryQty': currentStrawberryQty,
          'updatedAppleQty': updatedAppleQty,
          'updatedVarianceQty': updatedVarianceQty,
          'updatedStrawberryQty': updatedStrawberryQty,
        });

        // Commit the batch
        await batch.commit();

        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text('Sales register submitted successfully!'),
        ));
        // Clear the inputs after submission
        _mondAppleQtyController.clear();
        _mondMintQtyController.clear();
        _mondStrawberryQtyController.clear();
        setState(() {
          _selectedDsName = null;
        });
      } else {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text('Please select a Salesman Name and fill all quantities.'),
        ));
        setState(() {
          _isSubmitting = false; // Reset flag if validation fails
        });
      }
    } catch (e) {
      print('Error submitting sales register: $e');
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Failed to submit sales register: $e'),
      ));
    } finally {
      // Reset the flag regardless of success or failure
      setState(() {
        _isSubmitting = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Sales Register'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
            _buildDropdownField('Salesman Name', _selectedDsName, _dsNames, (value) {
              setState(() {
                _selectedDsName = value;
              });
            }),
            SizedBox(height: 20),
            Center(
              child: Text(
                'Qty Issued',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.black),
              ),
            ),
            SizedBox(height: 10),
            _buildSalesTable(),
            SizedBox(height: 20),
            Center(
              child: ElevatedButton(
                // Enable button only when not submitting, a DS is selected, and at least one quantity is non-zero
                onPressed: (_isSubmitting || _selectedDsName == null || !_hasNonZeroQuantity)
                  ? null
                  : _submitSalesRegister,
                child: _isSubmitting
                  ? SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text('Submit'),
              ),
            ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDropdownField(String label, String? selectedValue, List<String?> items, ValueChanged<String?> onChanged) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: label,
        ),
        child: DropdownButtonHideUnderline(
          child: DropdownButton<String?>(
            value: selectedValue,
            isExpanded: true,
            onChanged: onChanged,
            items: items.map((item) {
              return DropdownMenuItem(
                value: item,
                child: Text(item ?? 'Select $label'),
              );
            }).toList(),
          ),
        ),
      ),
    );
  }

  Widget _buildSalesTable() {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 10),
      child: Table(
        border: TableBorder.all(),
        columnWidths: {
          0: FlexColumnWidth(2),    // Item name column (wider)
          1: FixedColumnWidth(70),  // Qty input column (fixed width for 4 digits)
          2: FlexColumnWidth(1),    // BASE column
          3: FlexColumnWidth(1),    // T.Qty column
        },
        children: [
          TableRow(
            decoration: BoxDecoration(color: Colors.grey[300]),
            children: [
              _buildTableHeaderCell('Item'),
              _buildTableHeaderCell('Qty'),
              _buildTableHeaderCell('BASE'),
              _buildTableHeaderCell('T.Qty(M)'),
            ],
          ),
          _buildOrderRow('Variance', _mondMintQtyController),
          _buildOrderRow('Apple', _mondAppleQtyController),
          _buildOrderRow('Strawberry', _mondStrawberryQtyController),
          _buildTotalRow(),
        ],
      ),
    );
  }

  void _updateTotals() {
    setState(() {
      // This will trigger a rebuild with updated calculations
    });

    // Check if any quantity is non-zero to update submit button state
    _checkQuantities();
  }

  TableRow _buildOrderRow(String itemName, TextEditingController qtyController) {
    return TableRow(
      children: [
        _buildTableCell(Text(itemName)),
        _buildTableCell(
          Container(
            width: 60, // Fixed width to accommodate 4 digits comfortably
            child: TextFormField(
              controller: qtyController,
              decoration: InputDecoration(
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(horizontal: 4, vertical: 0),
                isDense: true, // Makes the input more compact
              ),
              keyboardType: TextInputType.number,
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 14), // Slightly smaller font size
              // Only allow digits (0-9)
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
              validator: (value) {
                return null;
              },
              onChanged: (value) {
                _updateTotals();
              },
            ),
          ),
        ),
        _buildTableCell(Text('0.02'), greyedOut: true), // BASE is fixed at 0.02
        _buildTableCell(Text((_calculateTQty(qtyController)).toStringAsFixed(2)), greyedOut: true),
      ],
    );
  }

  TableRow _buildTotalRow() {
    return TableRow(
      children: [
        _buildTableCell(Text('TOTAL')),
        _buildTableCell(Text(_calculateTotalQty().toString())),
        _buildTableCell(Text('')),
        _buildTableCell(Text(_calculateTotalTQty().toStringAsFixed(2))),
      ],
    );
  }

  Widget _buildTableHeaderCell(String text) {
    return Container(
      padding: const EdgeInsets.all(8.0),
      color: Colors.grey[300],
      child: Center(child: Text(text)),
    );
  }

  Widget _buildTableCell(Widget child, {bool greyedOut = false}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 6.0),
      color: greyedOut ? Colors.grey[300] : null,
      constraints: BoxConstraints(minHeight: 40), // Ensure consistent height
      child: Center(child: child),
    );
  }

  int _calculateTotalQty() {
    return (int.tryParse(_mondAppleQtyController.text) ?? 0) +
        (int.tryParse(_mondMintQtyController.text) ?? 0) +
        (int.tryParse(_mondStrawberryQtyController.text) ?? 0);
  }

  double _calculateTotalTQty() {
    return (_calculateTQty(_mondAppleQtyController) +
        _calculateTQty(_mondMintQtyController) +
        _calculateTQty(_mondStrawberryQtyController));
  }

  double _calculateTQty(TextEditingController qtyController) {
    return (double.tryParse(qtyController.text) ?? 0) * 0.02;
  }

  Future<bool> _showConfirmationDialog() async {
    // Get the current DS quantities
    Map<String, int> currentQty = await _getCurrentDsQuantities();

    // Parse new quantities
    int newAppleQty = int.tryParse(_mondAppleQtyController.text) ?? 0;
    int newVarianceQty = int.tryParse(_mondMintQtyController.text) ?? 0;
    int newStrawberryQty = int.tryParse(_mondStrawberryQtyController.text) ?? 0;

    // Calculate updated quantities
    int updatedAppleQty = currentQty['apple']! + newAppleQty;
    int updatedVarianceQty = currentQty['variance']! + newVarianceQty;
    int updatedStrawberryQty = currentQty['strawberry']! + newStrawberryQty;

    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Please Verify Quantities'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Please check the quantities entered before final submission:',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                SizedBox(height: 10),

                // New quantities section
                Text('New Quantities:', style: TextStyle(fontWeight: FontWeight.bold)),
                _buildConfirmationRow('Variance', _mondMintQtyController.text),
                _buildConfirmationRow('Apple', _mondAppleQtyController.text),
                _buildConfirmationRow('Strawberry', _mondStrawberryQtyController.text),
                _buildConfirmationRow('Total Quantity', _calculateTotalQty().toString()),
                _buildConfirmationRow('Total T.Qty(M)', _calculateTotalTQty().toStringAsFixed(2)),

                Divider(),

                // Current quantities section
                Text('Current Salesman Quantities:', style: TextStyle(fontWeight: FontWeight.bold)),
                _buildConfirmationRow('Variance', currentQty['variance'].toString()),
                _buildConfirmationRow('Apple', currentQty['apple'].toString()),
                _buildConfirmationRow('Strawberry', currentQty['strawberry'].toString()),

                Divider(),

                // Updated quantities section
                Text('Updated Salesman Quantities:', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.green)),
                _buildConfirmationRow('Variance', updatedVarianceQty.toString()),
                _buildConfirmationRow('Apple', updatedAppleQty.toString()),
                _buildConfirmationRow('Strawberry', updatedStrawberryQty.toString()),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: Text('Cancel'),
              onPressed: () {
                Navigator.of(context).pop(false);
              },
            ),
            TextButton(
              child: Text('Confirm'),
              onPressed: () {
                Navigator.of(context).pop(true);
              },
            ),
          ],
        );
      },
    ) ?? false; // Return false if dialog is dismissed
  }

  Future<Map<String, int>> _getCurrentDsQuantities() async {
    Map<String, int> result = {
      'apple': 0,
      'variance': 0,
      'strawberry': 0,
    };

    if (_selectedDsName == null) return result;

    try {
      String dsId = _selectedDsName!.split('_').last;

      QuerySnapshot dsQuery = await FirebaseFirestore.instance
          .collection('users')
          .where('dsId', isEqualTo: dsId)
          .limit(1)
          .get();

      if (dsQuery.docs.isNotEmpty) {
        DocumentSnapshot dsDoc = dsQuery.docs.first;
        Map<String, dynamic> data = dsDoc.data() as Map<String, dynamic>;

        result['apple'] = data['appleQty'] ?? 0;
        result['variance'] = data['varianceQty'] ?? 0;
        result['strawberry'] = data['strawberryQty'] ?? 0;
      }
    } catch (e) {
      print('Error fetching current quantities: $e');
    }

    return result;
  }

  Widget _buildConfirmationRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label + ':'),
          Text(value, style: TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  Widget _buildQuantityField(String label, TextEditingController controller) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              label,
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            flex: 2,
            child: TextFormField(
              controller: controller,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                border: OutlineInputBorder(),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
