package com.example.wd_panel

import io.flutter.app.FlutterApplication
import android.content.Context
import android.util.Log

class CustomApplication : FlutterApplication() {
    override fun onCreate() {
        super.onCreate()

        // Disable keystore access by setting security properties
        try {
            System.setProperty("keystore.type", "NONE")
            System.setProperty("javax.net.ssl.trustStoreType", "NONE")
        } catch (e: Exception) {
            Log.e("CustomApplication", "Error setting keystore properties: ${e.message}")
        }
    }

    override fun attachBaseContext(base: Context) {
        super.attachBaseContext(base)

        // Disable keystore access in the base context
        try {
            System.setProperty("keystore.type", "NONE")
            System.setProperty("javax.net.ssl.trustStoreType", "NONE")
        } catch (e: Exception) {
            Log.e("CustomApplication", "Error setting keystore properties in attachBaseContext: ${e.message}")
        }
    }
}
