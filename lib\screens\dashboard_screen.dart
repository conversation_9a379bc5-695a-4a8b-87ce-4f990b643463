import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:wd_panel/screens/profile_screen.dart';
import 'package:wd_panel/screens/reports_screen.dart';
import 'package:wd_panel/services/notification_service.dart'; // Import the NotificationService
import 'create_beat.dart';
import 'my_network_screen.dart';
import 'sales_register_screen.dart'; // Import the Sales Register Screen
import 'sign_up_screen_ds.dart';
import 'notifications_screen.dart'; // Import the Notifications Screen
import 'dart:io'; // Import to use exit()
import 'dart:async'; // Import for StreamSubscription and Timer
import '../widgets/language_selector.dart'; // Import the language selector
import '../l10n/app_localizations.dart'; // Import localizations

class DashboardScreen extends StatefulWidget {
  @override
  _DashboardScreenState createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  String? userName;
  DateTime? lastBackPressed;
  int _notificationCount = 0;
  StreamSubscription<QuerySnapshot>? _notificationSubscription;

  @override
  void initState() {
    super.initState();
    _fetchUserName();
    _setupNotificationListener();

    // Check for notifications after a short delay to ensure everything is initialized
    Future.delayed(Duration(seconds: 2), () {
      if (mounted) {
        _checkForNotifications();
      }
    });
  }

  Timer? _cleanupTimer;

  @override
  void dispose() {
    _notificationSubscription?.cancel();
    _cleanupTimer?.cancel();
    super.dispose();
  }

  Future<void> _setupNotificationListener() async {
    try {
      User? user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        print('Setting up notification listener for user: ${user.uid}');

        // First try to find the user document by authUid field
        QuerySnapshot userQuery = await FirebaseFirestore.instance
            .collection('wdUsers')
            .where('authUid', isEqualTo: user.uid)
            .limit(1)
            .get();

        // If no document found with authUid, try to find by phone number
        if (userQuery.docs.isEmpty) {
          // Extract phone number from Firebase user
          String? phoneNumber = user.phoneNumber;
          if (phoneNumber != null && phoneNumber.startsWith('+91')) {
            // Remove the +91 prefix to match our stored format
            String mobileNumber = phoneNumber.substring(3);

            userQuery = await FirebaseFirestore.instance
                .collection('wdUsers')
                .where('mobileNumber', isEqualTo: mobileNumber)
                .limit(1)
                .get();
          }
        }

        if (userQuery.docs.isEmpty) {
          print('User document does not exist in wdUsers collection');
          return;
        }

        // Get the first matching document
        DocumentSnapshot userDoc = userQuery.docs.first;

        // Update the authUid field if it's not set
        if (userDoc['authUid'] == null) {
          await FirebaseFirestore.instance
              .collection('wdUsers')
              .doc(userDoc.id)
              .update({'authUid': user.uid});
        }

        String wdId = userDoc['wdId'];
        print('WD ID for notification listener: $wdId');

        // Create test notifications if needed (only for development)
        await _createTestNotificationsIfNeeded(wdId);

        print('Setting up notification listener for WD ID: $wdId');

        // First, do a one-time query to check for existing notifications
        QuerySnapshot initialQuery = await FirebaseFirestore.instance
            .collection('wd_notifications')
            .where('wdId', isEqualTo: wdId)
            .where('isRead', isEqualTo: false)
            .get();

        print('Initial query found ${initialQuery.docs.length} unread notifications');

        // Log each notification from the initial query
        for (var doc in initialQuery.docs) {
          Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
          print('Initial unread notification: id=${doc.id}, title=${data['title']}, wdId=${data['wdId']}, isRead=${data['isRead']}');
        }

        // Update the notification count from the initial query
        setState(() {
          _notificationCount = initialQuery.docs.length;
          print('Updated notification count to: $_notificationCount from initial query');
        });

        // Listen for unread notifications
        _notificationSubscription = FirebaseFirestore.instance
            .collection('wd_notifications')
            .where('wdId', isEqualTo: wdId)
            .where('isRead', isEqualTo: false)
            .snapshots()
            .listen((snapshot) {
              print('Notification update: ${snapshot.docs.length} unread notifications');
              print('Notification query parameters: wdId=$wdId, isRead=false');

              // Log each notification for debugging
              for (var doc in snapshot.docs) {
                Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
                print('Unread notification: id=${doc.id}, title=${data['title']}, wdId=${data['wdId']}, isRead=${data['isRead']}');
              }

              setState(() {
                _notificationCount = snapshot.docs.length;
                print('Updated notification count to: $_notificationCount from listener');
              });
            }, onError: (error) {
              print('Error in notification listener: $error');
            });

        // Set up a timer to periodically clean up old read notifications
        _cleanupTimer = Timer.periodic(Duration(days: 1), (timer) {
          _cleanupOldReadNotifications(wdId);
        });

        // Run cleanup once at startup
        _cleanupOldReadNotifications(wdId);
      }
    } catch (e) {
      print('Error setting up notification listener: $e');
    }
  }

  Future<void> _createTestNotificationsIfNeeded(String wdId) async {
    try {
      // Check if notifications exist
      QuerySnapshot existingNotifications = await FirebaseFirestore.instance
          .collection('wd_notifications')
          .where('wdId', isEqualTo: wdId)
          .limit(1)
          .get();

      // If no notifications exist, create some test ones
      if (existingNotifications.docs.isEmpty) {
        print('Creating test notifications for dashboard');

        // Create a batch for multiple writes
        WriteBatch batch = FirebaseFirestore.instance.batch();

        // Add a welcome notification
        DocumentReference welcomeRef = FirebaseFirestore.instance.collection('wd_notifications').doc();
        batch.set(welcomeRef, {
          'title': 'Welcome to Distributor',
          'message': 'Thank you for using the Distributor app. This is your notifications center where you\'ll receive important updates.',
          'timestamp': Timestamp.now(),
          'wdId': wdId,
          'isRead': false,
          'type': 'welcome',
        });

        // Add a feature notification
        DocumentReference featureRef = FirebaseFirestore.instance.collection('wd_notifications').doc();
        batch.set(featureRef, {
          'title': 'New Features Available',
          'message': 'Check out the new sales register and reporting features in the app!',
          'timestamp': Timestamp.fromDate(DateTime.now().subtract(Duration(hours: 2))),
          'wdId': wdId,
          'isRead': false,
          'type': 'feature',
        });

        // Add a reminder notification
        DocumentReference reminderRef = FirebaseFirestore.instance.collection('wd_notifications').doc();
        batch.set(reminderRef, {
          'title': 'Daily Reminder',
          'message': 'Don\'t forget to check your sales reports for today.',
          'timestamp': Timestamp.fromDate(DateTime.now().subtract(Duration(days: 1))),
          'wdId': wdId,
          'isRead': false,
          'type': 'reminder',
        });

        // Commit the batch
        await batch.commit();
        print('Test notifications created for dashboard');
      }
    } catch (e) {
      print('Error creating test notifications: $e');
    }
  }

  Future<void> _fetchUserName() async {
    try {
      User? user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        // First try to find the user document by authUid field
        QuerySnapshot userQuery = await FirebaseFirestore.instance
            .collection('wdUsers')
            .where('authUid', isEqualTo: user.uid)
            .limit(1)
            .get();

        // If no document found with authUid, try to find by phone number
        if (userQuery.docs.isEmpty) {
          // Extract phone number from Firebase user
          String? phoneNumber = user.phoneNumber;
          if (phoneNumber != null && phoneNumber.startsWith('+91')) {
            // Remove the +91 prefix to match our stored format
            String mobileNumber = phoneNumber.substring(3);

            userQuery = await FirebaseFirestore.instance
                .collection('wdUsers')
                .where('mobileNumber', isEqualTo: mobileNumber)
                .limit(1)
                .get();
          }
        }

        if (userQuery.docs.isEmpty) {
          print('User document does not exist in wdUsers collection');
          setState(() {
            userName = 'USER DATA NOT FOUND';
          });

          // Show a message to the user
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('User data not found. Please log out and log in again.'),
            duration: Duration(seconds: 5),
          ));

          // Sign out after a delay
          Future.delayed(Duration(seconds: 5), () {
            _signOut();
          });

          return;
        }

        // Get the first matching document
        DocumentSnapshot userDoc = userQuery.docs.first;

        // Update the authUid field if it's not set
        if (userDoc['authUid'] == null) {
          await FirebaseFirestore.instance
              .collection('wdUsers')
              .doc(userDoc.id)
              .update({'authUid': user.uid});
        }

        setState(() {
          userName = (userDoc['name'] as String).toUpperCase();
        });
      }
    } catch (e) {
      print('Failed to fetch user name: $e');
      setState(() {
        userName = 'ERROR LOADING DATA';
      });

      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Error loading user data. Please try again later.'),
      ));
    }
  }

  Future<void> _cleanupOldReadNotifications(String wdId) async {
    try {
      print('Cleaning up old read notifications');

      // Get timestamp for 7 days ago
      final DateTime sevenDaysAgo = DateTime.now().subtract(Duration(days: 7));
      final Timestamp timestamp = Timestamp.fromDate(sevenDaysAgo);

      // Use a simpler query to avoid index issues
      // First, get all read notifications for this WD
      QuerySnapshot readNotifications = await FirebaseFirestore.instance
          .collection('wd_notifications')
          .where('wdId', isEqualTo: wdId)
          .where('isRead', isEqualTo: true)
          .get();

      print('Found ${readNotifications.docs.length} read notifications');

      // Filter locally for old notifications
      List<DocumentSnapshot> oldNotifications = readNotifications.docs.where((doc) {
        Timestamp docTimestamp = doc['timestamp'] as Timestamp;
        return docTimestamp.compareTo(timestamp) < 0;
      }).toList();

      if (oldNotifications.isEmpty) {
        print('No old read notifications to clean up');
        return;
      }

      print('Found ${oldNotifications.length} old read notifications to clean up');

      // Create a batch for multiple deletes
      WriteBatch batch = FirebaseFirestore.instance.batch();

      // Add each old notification to the batch for deletion
      for (var doc in oldNotifications) {
        batch.delete(doc.reference);
      }

      // Commit the batch
      await batch.commit();

      print('Cleaned up ${oldNotifications.length} old read notifications');
    } catch (e) {
      print('Error cleaning up old notifications: $e');
    }
  }

  // Function to manually check for notifications
  Future<void> _checkForNotifications() async {
    try {
      User? user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        // First try to find the user document by authUid field
        QuerySnapshot userQuery = await FirebaseFirestore.instance
            .collection('wdUsers')
            .where('authUid', isEqualTo: user.uid)
            .limit(1)
            .get();

        // If no document found with authUid, try to find by phone number
        if (userQuery.docs.isEmpty) {
          // Extract phone number from Firebase user
          String? phoneNumber = user.phoneNumber;
          if (phoneNumber != null && phoneNumber.startsWith('+91')) {
            // Remove the +91 prefix to match our stored format
            String mobileNumber = phoneNumber.substring(3);

            userQuery = await FirebaseFirestore.instance
                .collection('wdUsers')
                .where('mobileNumber', isEqualTo: mobileNumber)
                .limit(1)
                .get();
          }
        }

        if (userQuery.docs.isEmpty) {
          print('User document does not exist in wdUsers collection');
          return;
        }

        // Get the first matching document
        DocumentSnapshot userDoc = userQuery.docs.first;
        String wdId = userDoc['wdId'];

        print('Manually checking for notifications for WD ID: $wdId');

        // Query for unread notifications
        QuerySnapshot notificationsQuery = await FirebaseFirestore.instance
            .collection('wd_notifications')
            .where('wdId', isEqualTo: wdId)
            .where('isRead', isEqualTo: false)
            .get();

        print('Manual check found ${notificationsQuery.docs.length} unread notifications');

        // Log each notification for debugging
        for (var doc in notificationsQuery.docs) {
          Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
          print('Found notification: id=${doc.id}, title=${data['title']}, wdId=${data['wdId']}, isRead=${data['isRead']}');
        }

        // Update the notification count
        setState(() {
          _notificationCount = notificationsQuery.docs.length;
          print('Updated notification count to: $_notificationCount');
        });
      }
    } catch (e) {
      print('Error checking for notifications: $e');
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Error checking for notifications: $e'),
      ));
    }
  }

  Future<void> _createTestNotification() async {
    try {
      // Use the direct notification method from NotificationService
      DateTime now = DateTime.now();
      String title = 'Test Notification';
      String message = 'This is a test notification created at ${now.toString()}';

      await NotificationService.createDirectNotification(title, message, type: 'test');

      // Manually check for notifications after creating a new one
      await Future.delayed(Duration(milliseconds: 500)); // Small delay to ensure Firestore updates
      _checkForNotifications();

      print('Test notification created successfully');
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Test notification created'),
        duration: Duration(seconds: 2),
      ));
    } catch (e) {
      print('Error creating test notification: $e');
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Error creating test notification: $e'),
      ));
    }
  }

  Future<void> _signOut() async {
    try {
      User? user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        try {
          // First try to find the user document by authUid field
          QuerySnapshot userQuery = await FirebaseFirestore.instance
              .collection('wdUsers')
              .where('authUid', isEqualTo: user.uid)
              .limit(1)
              .get();

          // If no document found with authUid, try to find by phone number
          if (userQuery.docs.isEmpty) {
            // Extract phone number from Firebase user
            String? phoneNumber = user.phoneNumber;
            if (phoneNumber != null && phoneNumber.startsWith('+91')) {
              // Remove the +91 prefix to match our stored format
              String mobileNumber = phoneNumber.substring(3);

              userQuery = await FirebaseFirestore.instance
                  .collection('wdUsers')
                  .where('mobileNumber', isEqualTo: mobileNumber)
                  .limit(1)
                  .get();
            }
          }

          if (userQuery.docs.isNotEmpty) {
            // Get the first matching document
            DocumentSnapshot userDoc = userQuery.docs.first;

            // Clear the deviceId in Firestore
            await FirebaseFirestore.instance
                .collection('wdUsers')
                .doc(userDoc.id)
                .update({'deviceId': null});
          }
        } catch (e) {
          // Ignore errors when updating Firestore, just log them
          print('Error updating deviceId: $e');
        }

        // Sign out the user
        await FirebaseAuth.instance.signOut();

        // Navigate to the login screen
        Navigator.pushNamedAndRemoveUntil(context, '/login', (route) => false);
      }
    } catch (e) {
      print('Failed to sign out: $e');
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Failed to sign out: $e'),
      ));
    }
  }

  @override
  Widget build(BuildContext context) {
    // Debug print for notification count in build method
    print('Building dashboard with notification count: $_notificationCount');
    return WillPopScope(
      onWillPop: () async {
        // Handle double back press to exit the app
        if (lastBackPressed == null ||
            DateTime.now().difference(lastBackPressed!) > Duration(seconds: 2)) {
          lastBackPressed = DateTime.now();
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('Press back again to exit'),
            duration: Duration(seconds: 2),
          ));
          return false; // Prevents exiting on first back press
        } else {
          // Exits the app directly on the second back press
          exit(0);
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(AppLocalizations.of(context).translate('dashboard')),
          elevation: 0,
          actions: [
            // Language selector
            LanguageSelector(),
            // Notification icon with badge
            _buildNotificationIcon(),
          ],
        ),
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
              Theme.of(context).primaryColor.withOpacity(0.1),
              Theme.of(context).scaffoldBackgroundColor,
              ],
            ),
          ),
          child: SafeArea(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    if (userName != null)
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Text(
                            '${AppLocalizations.of(context).translate('welcome')}, $userName',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).primaryColor,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    SizedBox(height: 24),
                    _buildDashboardGrid(),
                    SizedBox(height: 24),
                    ElevatedButton.icon(
                      onPressed: _signOut,
                      icon: Icon(Icons.logout),
                      label: Text(AppLocalizations.of(context).translate('logout')),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.error,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDashboardGrid() {
    return GridView.count(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      children: [
        _buildDashboardCard(AppLocalizations.of(context).translate('profile'), Icons.person, () {
          Navigator.push(context, MaterialPageRoute(builder: (context) => ProfileScreen()));
        }),
        _buildDashboardCard(AppLocalizations.of(context).translate('my_network'), Icons.people, () {
          Navigator.push(context, MaterialPageRoute(builder: (context) => MyNetworkScreen()));
        }),
        _buildDashboardCard(AppLocalizations.of(context).translate('create_beat'), Icons.add_location, () {
          Navigator.push(context, MaterialPageRoute(builder: (context) => CreateBeatScreen()));
        }),
        _buildDashboardCard(AppLocalizations.of(context).translate('sales_register'), Icons.point_of_sale, () {
          Navigator.push(context, MaterialPageRoute(builder: (context) => SalesRegisterScreen()));
        }),
        _buildDashboardCard(AppLocalizations.of(context).translate('reports'), Icons.bar_chart, () {
          Navigator.push(context, MaterialPageRoute(builder: (context) => ReportsScreen()));
        }),
        _buildDashboardCard(AppLocalizations.of(context).translate('onboard_ds'), Icons.person_add, () {
          Navigator.push(context, MaterialPageRoute(builder: (context) => SignUpScreen()));
        }),
      ],
    );
  }

  // Build notification icon with badge
  Widget _buildNotificationIcon() {
    // Debug print for notification count
    print('Building notification icon with count: $_notificationCount');

    return Stack(
      alignment: Alignment.center,
      children: [
        GestureDetector(
          onLongPress: () {
            // For testing: Create a test notification on long press
            _createTestNotification();
          },
          child: IconButton(
            icon: Icon(Icons.notifications),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => NotificationsScreen()),
              ).then((_) {
                // Refresh notifications when returning from the notifications screen
                _checkForNotifications();
              });
            },
            tooltip: 'Notifications',
          ),
        ),
        if (_notificationCount > 0)
          Positioned(
            top: 8,
            right: 8,
            child: Container(
              padding: EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(10),
              ),
              constraints: BoxConstraints(
                minWidth: 16,
                minHeight: 16,
              ),
              child: Text(
                _notificationCount > 9 ? '9+' : _notificationCount.toString(),
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildDashboardCard(String title, IconData icon, VoidCallback onPressed) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(12),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 32,
              color: Theme.of(context).primaryColor,
            ),
            SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).textTheme.bodyLarge?.color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}