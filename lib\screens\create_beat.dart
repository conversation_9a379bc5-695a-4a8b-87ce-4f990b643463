import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

class CreateBeatScreen extends StatefulWidget {
  @override
  _CreateBeatScreenState createState() => _CreateBeatScreenState();
}

class _CreateBeatScreenState extends State<CreateBeatScreen> {
  bool _isSubmitting = false; // Flag to prevent multiple submission attempts
  bool _isLoading = true; // Flag to track initial data loading
  final _formKey = GlobalKey<FormState>(); // Form key for validation

  final _beatNameController = TextEditingController();
  final _locationController = TextEditingController();
  String? _selectedDsName;
  Map<String, bool> _selectedDays = {
    'Monday': false,
    'Tuesday': false,
    'Wednesday': false,
    'Thursday': false,
    'Friday': false,
    'Saturday': false,
    'Sunday': false,
  };

  List<String> _dsNames = [];
  final List<String> _daysOfWeek = [
    'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'
  ];

  // Map to store day icons
  final Map<String, IconData> _dayIcons = {
    'Monday': Icons.looks_one,
    'Tuesday': Icons.looks_two,
    'Wednesday': Icons.looks_3,
    'Thursday': Icons.looks_4,
    'Friday': Icons.looks_5,
    'Saturday': Icons.weekend,
    'Sunday': Icons.weekend,
  };

  @override
  void initState() {
    super.initState();
    _fetchDsNames();
  }

  @override
  void dispose() {
    _beatNameController.dispose();
    _locationController.dispose();
    super.dispose();
  }

  Future<void> _fetchDsNames() async {
    setState(() {
      _isLoading = true;
    });

    try {
      User? user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        // First try to find the user document by authUid field
        QuerySnapshot userQuery = await FirebaseFirestore.instance
            .collection('wdUsers')
            .where('authUid', isEqualTo: user.uid)
            .limit(1)
            .get();

        // If no document found with authUid, try to find by phone number
        if (userQuery.docs.isEmpty) {
          // Extract phone number from Firebase user
          String? phoneNumber = user.phoneNumber;
          if (phoneNumber != null && phoneNumber.startsWith('+91')) {
            // Remove the +91 prefix to match our stored format
            String mobileNumber = phoneNumber.substring(3);

            userQuery = await FirebaseFirestore.instance
                .collection('wdUsers')
                .where('mobileNumber', isEqualTo: mobileNumber)
                .limit(1)
                .get();
          }
        }

        if (userQuery.docs.isEmpty) {
          print('User document does not exist in wdUsers collection');
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('User data not found. Please log out and log in again.'),
            behavior: SnackBarBehavior.floating,
          ));
          setState(() {
            _isLoading = false;
          });
          return;
        }

        // Get the first matching document
        DocumentSnapshot userDoc = userQuery.docs.first;

        // Update the authUid field if it's not set
        if (userDoc['authUid'] == null) {
          await FirebaseFirestore.instance
              .collection('wdUsers')
              .doc(userDoc.id)
              .update({'authUid': user.uid});
        }

        String wdCode = userDoc['wdId'];

        // Only fetch DS users associated with this WD
        QuerySnapshot querySnapshot = await FirebaseFirestore.instance
            .collection('users')
            .where('wdCode', isEqualTo: wdCode)
            .get();

        setState(() {
          _dsNames = querySnapshot.docs.map((doc) {
            String name = doc['name'] as String;
            String dsId = doc['dsId'] as String;
            return '$name\_$dsId';  // Create unique DS Name by combining name and dsId
          }).toList();
          _selectedDsName = null; // Reset selected value when fetching new data
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error fetching Salesman names: $e');
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Error loading data. Please try again later.'),
        behavior: SnackBarBehavior.floating,
      ));
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _submitBeat() async {
    // Validate form
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Prevent multiple submission attempts
    if (_isSubmitting) return;

    final beatName = _beatNameController.text.trim();
    final location = _locationController.text.trim();

    // Get selected days as a list
    final selectedDays = _selectedDays.entries
        .where((entry) => entry.value)
        .map((entry) => entry.key)
        .toList();

    if (selectedDays.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Please select at least one day of visit'),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    setState(() {
      _isSubmitting = true; // Set flag to prevent multiple attempts
    });

    try {
      User? user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        // First try to find the user document by authUid field
        QuerySnapshot userQuery = await FirebaseFirestore.instance
            .collection('wdUsers')
            .where('authUid', isEqualTo: user.uid)
            .limit(1)
            .get();

        // If no document found with authUid, try to find by phone number
        if (userQuery.docs.isEmpty) {
          // Extract phone number from Firebase user
          String? phoneNumber = user.phoneNumber;
          if (phoneNumber != null && phoneNumber.startsWith('+91')) {
            // Remove the +91 prefix to match our stored format
            String mobileNumber = phoneNumber.substring(3);

            userQuery = await FirebaseFirestore.instance
                .collection('wdUsers')
                .where('mobileNumber', isEqualTo: mobileNumber)
                .limit(1)
                .get();
          }
        }

        if (userQuery.docs.isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('User data not found. Please log out and log in again.'),
              behavior: SnackBarBehavior.floating,
            ),
          );
          setState(() {
            _isSubmitting = false; // Reset flag if user data not found
          });
          return;
        }

        // Get the first matching document
        DocumentSnapshot userDoc = userQuery.docs.first;

        // Update the authUid field if it's not set
        if (userDoc['authUid'] == null) {
          await FirebaseFirestore.instance
              .collection('wdUsers')
              .doc(userDoc.id)
              .update({'authUid': user.uid});
        }

        String wdId = userDoc['wdId'];

        // Extract dsId from the selected DS name (format: name_dsId)
        String? dsId;
        if (_selectedDsName != null) {
          List<String> parts = _selectedDsName!.split('_');
          if (parts.length > 1) {
            dsId = parts.last; // Get the dsId part
          }
        }

        // Find the highest beat number from existing beatIds
        QuerySnapshot beatSnapshot = await FirebaseFirestore.instance.collection('beats').get();
        int maxBeatNumber = 0;
        for (var doc in beatSnapshot.docs) {
          final data = doc.data() as Map<String, dynamic>;
          final beatId = data['beatId'] as String?;
          if (beatId != null && beatId.startsWith('BT-')) {
            final parts = beatId.split('-');
            if (parts.length > 1) {
              final numStr = parts[1];
              final num = int.tryParse(numStr);
              if (num != null && num > maxBeatNumber) {
                maxBeatNumber = num;
              }
            }
          }
        }
        int nextBeatNumber = maxBeatNumber + 1;
        String beatNumber = nextBeatNumber.toString().padLeft(6, '0');
        String beatId = 'BT-$beatNumber-${location.replaceAll(' ', '')}-$dsId';

        await FirebaseFirestore.instance.collection('beats').doc(beatId).set({
          'beatId': beatId,
          'beatName': beatName,
          'location': location,
          'dsName': _selectedDsName,
          'dsId': dsId, // Add dsId to the beats collection
          'daysOfVisit': selectedDays, // Store as array of selected days
          'createdBy': wdId,
          'createdAt': Timestamp.now(),
        });

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 12),
                Text('Beat created successfully!'),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            duration: Duration(seconds: 2), // Short duration before redirect
          ),
        );

        // Clear the inputs after submission
        _beatNameController.clear();
        _locationController.clear();
        setState(() {
          _selectedDsName = null;
          // Reset all checkboxes
          _selectedDays.updateAll((key, value) => false);
        });

        // Redirect to Dashboard after a short delay
        Future.delayed(Duration(seconds: 1), () {
          if (mounted) {
            Navigator.of(context).pushReplacementNamed('/dashboard');
          }
        });
      }
    } catch (e) {
      print('Error creating beat: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to create beat: $e'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    } finally {
      // Reset the flag regardless of success or failure
      setState(() {
        _isSubmitting = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Create Beat'),
        elevation: 0,
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : _buildContent(),
    );
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Section
          _buildHeaderSection(),

          // Form Section
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Basic Information Card
                  _buildCard(
                    title: 'Beat Information',
                    icon: Icons.location_on,
                    children: [
                      _buildTextField(
                        'Beat Name',
                        _beatNameController,
                        Icons.edit_location_alt,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter a beat name';
                          }
                          return null;
                        },
                      ),
                      SizedBox(height: 4),
                      _buildTextField(
                        'Location',
                        _locationController,
                        Icons.place,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter a location';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),

                  SizedBox(height: 16),

                  // DS Selection Card
                  _buildCard(
                    title: 'Salesman Selection',
                    icon: Icons.person,
                    children: [
                      _buildDropdownField(
                        'Salesman Name',
                        _selectedDsName,
                        _dsNames,
                        Icons.person_outline,
                        (value) {
                          setState(() {
                            _selectedDsName = value;
                          });
                        },
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please select a Salesman';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),

                  SizedBox(height: 16),

                  // Days Selection Card
                  _buildCard(
                    title: 'Days of Visit',
                    icon: Icons.calendar_today,
                    children: [
                      _buildDaysSelection(),
                    ],
                  ),

                  SizedBox(height: 24),

                  // Submit Button
                  Center(
                    child: _buildSubmitButton(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withOpacity(0.8),
          ],
        ),
      ),
      padding: EdgeInsets.symmetric(vertical: 0, horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.add_location_alt,
                  color: Colors.white,
                  size: 32,
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Create New Beat',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
        ],
      ),
    );
  }

  Widget _buildCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: Theme.of(context).primaryColor,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8),
            Divider(),
            SizedBox(height: 8),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildTextField(
    String label,
    TextEditingController controller,
    IconData icon, {
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      validator: validator,
    );
  }

  Widget _buildDropdownField(
    String label,
    String? selectedValue,
    List<String> items,
    IconData icon,
    ValueChanged<String?> onChanged, {
    String? Function(String?)? validator,
  }) {
    return DropdownButtonFormField<String>(
      value: items.contains(selectedValue) ? selectedValue : null,
      isExpanded: true,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      onChanged: onChanged,
      items: items.map((item) {
        // Extract just the name part for display (before the underscore)
        String displayName = item.split('_').first;

        return DropdownMenuItem(
          value: item,
          child: Text(displayName),
        );
      }).toList(),
      hint: Text('Select $label'),
      validator: validator,
    );
  }

  Widget _buildDaysSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select days when the Salesman will visit this beat:',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[700],
          ),
        ),
        SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _daysOfWeek.map((day) {
            bool isSelected = _selectedDays[day] ?? false;
            return InkWell(
              onTap: () {
                setState(() {
                  _selectedDays[day] = !isSelected;
                });
              },
              borderRadius: BorderRadius.circular(8),
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected
                      ? Theme.of(context).primaryColor.withOpacity(0.1)
                      : Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isSelected
                        ? Theme.of(context).primaryColor
                        : Colors.grey[300]!,
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      isSelected ? Icons.check_circle : _dayIcons[day],
                      size: 16,
                      color: isSelected
                          ? Theme.of(context).primaryColor
                          : Colors.grey[600],
                    ),
                    SizedBox(width: 6),
                    Text(
                      day,
                      style: TextStyle(
                        color: isSelected
                            ? Theme.of(context).primaryColor
                            : Colors.grey[800],
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
        SizedBox(height: 8),
        // Quick selection buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TextButton.icon(
              onPressed: () {
                setState(() {
                  _selectedDays.updateAll((key, value) => true);
                });
              },
              icon: Icon(Icons.select_all, size: 16),
              label: Text('Select All'),
              style: TextButton.styleFrom(
                foregroundColor: Theme.of(context).primaryColor,
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                visualDensity: VisualDensity.compact,
              ),
            ),
            SizedBox(width: 8),
            TextButton.icon(
              onPressed: () {
                setState(() {
                  _selectedDays.updateAll((key, value) => false);
                });
              },
              icon: Icon(Icons.clear_all, size: 16),
              label: Text('Clear All'),
              style: TextButton.styleFrom(
                foregroundColor: Colors.grey[700],
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                visualDensity: VisualDensity.compact,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSubmitButton() {
    return ElevatedButton(
      onPressed: _isSubmitting ? null : _submitBeat,
      style: ElevatedButton.styleFrom(
        padding: EdgeInsets.symmetric(horizontal: 32, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        minimumSize: Size(200, 48),
      ),
      child: _isSubmitting
          ? Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                SizedBox(width: 12),
                Text('Creating Beat...'),
              ],
            )
          : Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.add_location),
                SizedBox(width: 8),
                Text('Create Beat'),
              ],
            ),
    );
  }
}
