import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:async';

class SignUpScreen extends StatefulWidget {
  @override
  _SignUpScreenState createState() => _SignUpScreenState();
}

class _SignUpScreenState extends State<SignUpScreen> {
  bool _isSigningUp = false; // Flag to prevent multiple signup attempts
  final _nameController = TextEditingController();
  final _mobileNumberController = TextEditingController();
  final _pincodeController = TextEditingController();
  final _cityController = TextEditingController();
  String? _selectedState;
  String _region = '';
  bool _isPincodeValid = false; // Flag to track if pincode is valid

  // Cache for pincode lookup results to avoid repeated API calls
  final Map<String, Map<String, String>> _pincodeCache = {};

  final _formKey = GlobalKey<FormState>();

  final Map<String, String> _stateRegions = {
    'Andhra Pradesh': 'South',
    'Arunachal Pradesh': 'East',
    'Assam': 'East',
    'Bihar': 'East',
    'Chhattisgarh': 'Central',
    'Goa': 'West',
    'Gujarat': 'West',
    'Haryana': 'North',
    'Himachal Pradesh': 'North',
    'Jharkhand': 'East',
    'Karnataka': 'South',
    'Kerala': 'South',
    'Madhya Pradesh': 'Central',
    'Maharashtra': 'West',
    'Manipur': 'East',
    'Meghalaya': 'East',
    'Mizoram': 'East',
    'Nagaland': 'East',
    'Odisha': 'East',
    'Punjab': 'North',
    'Rajasthan': 'North',
    'Sikkim': 'East',
    'Tamil Nadu': 'South',
    'Telangana': 'South',
    'Tripura': 'East',
    'Uttar Pradesh': 'North',
    'Uttarakhand': 'North',
    'West Bengal': 'East',
    'Delhi': 'North',
    'Jammu and Kashmir': 'North',
    'Ladakh': 'North',
    'Puducherry': 'South',
    'Lakshadweep': 'South',
    'Andaman and Nicobar Islands': 'South',
    'Chandigarh': 'North',
    'Dadra and Nagar Haveli and Daman and Diu': 'West',
  };

  @override
  void initState() {
    super.initState();
    // Add listener to pincode field
    _pincodeController.addListener(_onPincodeChanged);
  }

  @override
  void dispose() {
    // Remove listener when widget is disposed
    _pincodeController.removeListener(_onPincodeChanged);
    super.dispose();
  }

  void _updateRegion(String? selectedState) {
    setState(() {
      _region = _stateRegions[selectedState] ?? '';
    });
  }

  // Update region based on state from pincode
  void _updateRegionFromState(String state) {
    setState(() {
      final region = _stateRegions[state];
      if (region != null) {
        _region = region;
      } else {
        _region = '';
      }
    });
  }

  // Debounce timer for API calls
  DateTime? _lastPincodeChangeTime;

  void _onPincodeChanged() async {
    final pincode = _pincodeController.text.trim();

    // Reset fields if pincode is not 6 digits
    if (pincode.length != 6) {
      setState(() {
        _isPincodeValid = false;
        _selectedState = null;
        _region = '';
        _cityController.text = '';
      });
      return;
    }

    // Set a debounce of 500ms to avoid too many API calls while typing
    _lastPincodeChangeTime = DateTime.now();
    await Future.delayed(Duration(milliseconds: 500));
    if (_lastPincodeChangeTime != null &&
        DateTime.now().difference(_lastPincodeChangeTime!).inMilliseconds < 500) {
      return; // Another change was made, skip this one
    }

    // Show loading state
    setState(() {
      _selectedState = null; // Set to null instead of 'Loading...'
      _cityController.text = 'Loading...';
      _region = 'Loading...';
    });

    try {
      // Check if we have this pincode in cache
      if (_pincodeCache.containsKey(pincode)) {
        _updateFieldsFromPincodeData(_pincodeCache[pincode]!);
        return;
      }

      // Call the API to get pincode details
      final response = await http.get(
        Uri.parse('https://api.postalpincode.in/pincode/$pincode')
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);

        if (data.isNotEmpty && data[0]['Status'] == 'Success') {
          final List<dynamic> postOffices = data[0]['PostOffice'];

          if (postOffices.isNotEmpty) {
            final postOffice = postOffices[0];

            // Extract relevant information
            final Map<String, String> pincodeData = {
              'city': postOffice['Block'] ?? postOffice['Name'] ?? '',
              'state': postOffice['State'] ?? '',
              'district': postOffice['District'] ?? '',
            };

            // Cache the result
            _pincodeCache[pincode] = pincodeData;

            // Update the UI
            _updateFieldsFromPincodeData(pincodeData);
            return;
          }
        }

        // If we get here, the API didn't return valid data
        setState(() {
          _isPincodeValid = false;
          _selectedState = null;
          _region = '';
          _cityController.text = '';
        });
      } else {
        // API call failed
        setState(() {
          _isPincodeValid = false;
          _selectedState = null;
          _region = '';
          _cityController.text = '';
        });
      }
    } catch (e) {
      print('Error fetching pincode data: $e');
      setState(() {
        _isPincodeValid = false;
        _selectedState = null;
        _region = '';
        _cityController.text = '';
      });
    }
  }

  void _updateFieldsFromPincodeData(Map<String, String> pincodeData) {
    setState(() {
      final state = pincodeData['state'] ?? '';
      final city = pincodeData['city'] ?? '';
      final district = pincodeData['district'] ?? '';

      _isPincodeValid = true;
      _selectedState = state;

      // Set city (prefer Block/Name, fallback to District)
      _cityController.text = city.isNotEmpty ? city : district;

      // Update region based on state
      _updateRegionFromState(state);
    });
  }

  Future<void> _pickFile(bool isPan) async {
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(Icons.photo_camera),
              title: Text('Take a picture'),
              onTap: () async {
                Navigator.of(context).pop();
                final picker = ImagePicker();
                final pickedFile = await picker.pickImage(source: ImageSource.camera);
                if (pickedFile != null) {
                  final file = File(pickedFile.path);
                  _handleFileSelection(file, isPan);
                }
              },
            ),
            ListTile(
              leading: Icon(Icons.photo_library),
              title: Text('Choose from gallery'),
              onTap: () async {
                Navigator.of(context).pop();
                FilePickerResult? result = await FilePicker.platform.pickFiles();
                if (result != null) {
                  final file = File(result.files.single.path!);
                  _handleFileSelection(file, isPan);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  void _handleFileSelection(File file, bool isPan) async {
    int fileSizeInBytes = await file.length();
    int fileSizeInKB = fileSizeInBytes ~/ 1024;

    if (fileSizeInKB > 50) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('File size exceeds 50KB. Please upload a smaller file.'),
      ));
      return;
    }
  }

  Widget _buildStateField() {
    // Create a controller for the state field
    final stateController = TextEditingController(text: _selectedState ?? '');

    // If we're loading, show a loading indicator
    if (_cityController.text == 'Loading...') {
      return TextFormField(
        decoration: InputDecoration(
          labelText: 'State',
          border: OutlineInputBorder(),
          prefixIcon: Icon(Icons.location_on),
          filled: true,
          fillColor: Colors.grey[200],
          hintText: 'Loading...',
        ),
        enabled: false,
      );
    }

    // Otherwise, show a read-only text field with the state
    return TextFormField(
      controller: stateController,
      decoration: InputDecoration(
        labelText: 'State',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.location_on),
        filled: true,
        fillColor: Colors.grey[200],
      ),
      enabled: false,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'State is required. Please enter a valid pincode.';
        }
        return null;
      },
    );
  }

  Future<String> _uploadFile(String filePath) async {
    try {
      File file = File(filePath);
      String fileName = DateTime.now().millisecondsSinceEpoch.toString();
      TaskSnapshot snapshot = await FirebaseStorage.instance
          .ref('uploads/$fileName')
          .putFile(file);
      return await snapshot.ref.getDownloadURL();
    } catch (e) {
      print('Error uploading file: $e');
      return '';
    }
  }

  Future<String> _generateWdId() async {
    final QuerySnapshot querySnapshot = await FirebaseFirestore.instance
        .collection('wdUsers')
        .orderBy('wdId', descending: true)
        .limit(1)
        .get();

    if (querySnapshot.docs.isNotEmpty) {
      String lastWdId = querySnapshot.docs.first['wdId'];
      int lastIdNumber = int.parse(lastWdId.split('-').last);
      int newIdNumber = lastIdNumber + 1;
      return 'QK-WD-${newIdNumber.toString().padLeft(6, '0')}';
    } else {
      return 'QK-WD-000001';
    }
  }

  void _signUp() async {
    // Prevent multiple signup attempts
    if (_isSigningUp) return;

    if (_formKey.currentState!.validate()) {
      setState(() {
        _isSigningUp = true; // Set flag to prevent multiple attempts
      });

      final name = _nameController.text;
      final mobileNumber = _mobileNumberController.text;
      final pincode = _pincodeController.text;
      final city = _cityController.text;
      final email = '$<EMAIL>'; // Create email for reference
      // Use mobile number as password for Firebase Auth (will only be used internally)
      final password = mobileNumber;

      if (!_isPincodeValid && pincode.length == 6) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Please enter a valid pincode')),
        );
        setState(() {
          _isSigningUp = false;
        });
        return;
      }

      try {
        // Show loading indicator
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => Center(
            child: CircularProgressIndicator(),
          ),
        );

        // First, check if a user with this mobile number already exists in wdUsers collection
        QuerySnapshot existingWdUsers = await FirebaseFirestore.instance
            .collection('wdUsers')
            .where('mobileNumber', isEqualTo: mobileNumber)
            .get();

        if (existingWdUsers.docs.isNotEmpty) {
          // Close loading indicator
          Navigator.pop(context);

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('A WD user with this mobile number already exists. Please log in instead.')),
          );
          return;
        }

        // Also check if a user with this mobile number exists in users collection (DS users)
        QuerySnapshot existingDsUsers = await FirebaseFirestore.instance
            .collection('users')
            .where('mobileNumber', isEqualTo: mobileNumber)
            .get();

        // Close loading indicator
        Navigator.pop(context);

        if (existingDsUsers.docs.isNotEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('This mobile number is already registered. Please use a different mobile number.')),
          );
          return;
        }

        // Also check if the user exists in Firebase Authentication
        try {
          // Check if the phone number exists in Firebase Auth
          final List<String> signInMethods = await FirebaseAuth.instance.fetchSignInMethodsForEmail(email);

          // If the list is not empty, it means the email is already registered
          if (signInMethods.isNotEmpty) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('This mobile number is already registered in the authentication system. Please log in instead.')),
            );
            return;
          }
        } catch (e) {
          print('Error checking Firebase Authentication: $e');
          // Continue with signup even if this check fails
        }

        // Show loading indicator again
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => Center(
            child: CircularProgressIndicator(),
          ),
        );

        // Generate WD ID
        String wdId = await _generateWdId();

        // Create a new document with auto-generated ID
        DocumentReference docRef = await FirebaseFirestore.instance.collection('wdUsers').add({
          'name': name,
          'mobileNumber': mobileNumber,
          'pincode': pincode,
          'city': city,
          'state': _selectedState,
          'region': _region,
          'wdId': wdId,
          'kyc': 'pending',
          'status': 'active', // Default status is active
          'panUrl': '',
          'email': email, // Store email for reference
          'createdAt': FieldValue.serverTimestamp(),
        });

        // Sign out the user after registration
        await FirebaseAuth.instance.signOut();

        // Close loading indicator
        Navigator.pop(context);

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Successfully signed up! Please log in to continue.'),
            duration: Duration(seconds: 3),
          ),
        );

        // Navigate to login screen after a short delay
        await Future.delayed(Duration(seconds: 2));
        Navigator.pushReplacementNamed(context, '/login');
      } catch (e) {
        // Close loading indicator if it's showing
        Navigator.of(context, rootNavigator: true).pop();

        print('Error in sign up: $e');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to sign up: $e')),
        );
      } finally {
        // Reset the flag regardless of success or failure
        setState(() {
          _isSigningUp = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Sign Up'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Name Field
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: TextFormField(
                  controller: _nameController,
                  decoration: InputDecoration(
                    labelText: 'Name',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.person),
                    filled: true,
                    fillColor: Colors.grey[100],
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your name';
                    }
                    return null;
                  },
                ),
              ),

              // Mobile Number Field
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: TextFormField(
                  controller: _mobileNumberController,
                  decoration: InputDecoration(
                    labelText: 'Mobile Number',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.phone_android),
                    filled: true,
                    fillColor: Colors.grey[100],
                    hintText: '10-digit mobile number',
                  ),
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(10),
                  ],
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your mobile number';
                    } else if (value.length != 10) {
                      return 'Mobile number must be exactly 10 digits';
                    }
                    return null;
                  },
                ),
              ),

              // Pincode Field
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: TextFormField(
                  controller: _pincodeController,
                  decoration: InputDecoration(
                    labelText: 'Pincode',
                    hintText: 'Enter 6-digit pincode',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.location_on),
                    filled: true,
                    fillColor: Colors.grey[100],
                    suffixIcon: _isPincodeValid
                      ? Icon(Icons.check_circle, color: Colors.green)
                      : (_pincodeController.text.length == 6
                          ? Icon(Icons.error, color: Colors.red)
                          : null),
                  ),
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(6),
                  ],
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter pincode';
                    } else if (value.length != 6) {
                      return 'Pincode must be exactly 6 digits';
                    } else if (!_isPincodeValid) {
                      return 'Invalid pincode or region not recognized';
                    }
                    return null;
                  },
                ),
              ),

              // State Field (Auto-populated from pincode)
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: _buildStateField(),
              ),

              // City Field (Auto-populated based on pincode)
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: TextFormField(
                  controller: _cityController,
                  decoration: InputDecoration(
                    labelText: 'City',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.location_city),
                    filled: true,
                    fillColor: Colors.grey[200], // Darker background for disabled fields
                  ),
                  enabled: false,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'City is required';
                    }
                    return null;
                  },
                ),
              ),

              // Region Field (Auto-populated)
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: TextFormField(
                  readOnly: true,
                  decoration: InputDecoration(
                    labelText: 'Region',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.map),
                    filled: true,
                    fillColor: Colors.grey[200],
                  ),
                  controller: TextEditingController(text: _region),
                ),
              ),

              // Spacer
              SizedBox(height: 24.0),

              // Sign Up Button
              SizedBox(
                height: 50,
                child: ElevatedButton(
                  onPressed: _isSigningUp ? null : _signUp, // Disable button while signing up
                  style: ElevatedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                  ),
                  child: _isSigningUp
                    ? SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        'Submit',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                ),
              ),

              // Login Link
              Padding(
                padding: const EdgeInsets.only(top: 16.0),
                child: TextButton(
                  onPressed: () {
                    Navigator.pushReplacementNamed(context, '/login');
                  },
                  child: Text('Already have an account? Log In'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
