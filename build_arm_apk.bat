@echo off
echo Cleaning previous build...
flutter clean

echo Getting dependencies...
flutter pub get

echo Building ARM (32-bit) APK...
flutter build apk --release --target-platform=android-arm --split-debug-info=build/debug-info --tree-shake-icons

echo Build completed. APK is available at:
echo build\app\outputs\flutter-apk\app-release.apk

echo Copying APK to root directory...
copy build\app\outputs\flutter-apk\app-release.apk wd-panel-arm.apk

echo APK is now available as wd-panel-arm.apk in the root directory
