import 'dart:convert';
import 'dart:io';
import 'package:crypto/crypto.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:http/http.dart' as http;
import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:firebase_app_check_platform_interface/firebase_app_check_platform_interface.dart';

/// Custom App Check Provider for direct APK distribution
/// This provider communicates with your backend to get App Check tokens
/// Perfect for apps distributed outside Google Play Store
class CustomAppCheckProvider {
  static const String _appSecret = 'quickk-ds-panel-custom-secret-2024';

  // Your fixed app signature value as provided
  static const String _appSignature = '6b:0a:d1:89:f9:17:af:e9:8f:ab:06:2e:fd:e1:7a:ce:0c:89:fd:00:8d:0e:88:00:8d:54:e3:37:c6:23:cb:9c';

  // Backend Firebase Functions URL
  static const String _backendUrl = 'https://generateappchecktoken-5yw2qsexaa-uc.a.run.app';

  /// Generate custom credentials for backend validation
  static Future<Map<String, dynamic>> _generateCustomCredentials() async {
    try {
      // Get device information
      final deviceInfo = await _getDeviceInfo();

      // Use fixed app signature
      final appSignature = _appSignature;

      // Get current timestamp (milliseconds since epoch)
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      // Compose payload string exactly as backend expects
      final payload = '$_appSecret:$appSignature:$deviceInfo:$timestamp';

      // Generate SHA-256 hash for deviceSecret
      final deviceSecret = _generateSHA256(payload);

      // Debug print inputs
      print('🔍 [DEBUG] Payload for hashing: $payload');
      print('🔍 [DEBUG] deviceSecret (SHA-256): $deviceSecret');

      return {
        'deviceSecret': deviceSecret,
        'appSignature': appSignature,
        'deviceInfo': deviceInfo,
        'timestamp': timestamp,
      };
    } catch (e) {
      print('❌ Error generating custom credentials: $e');
      rethrow;
    }
  }

  /// Request App Check token from backend
  static Future<String> requestAppCheckTokenFromBackend() async {
    try {
      print('🔄 Requesting App Check token from backend...');
      print('🌐 Backend URL: $_backendUrl');

      // Step 0: Test basic connectivity
      try {
        final connectivityTest = await http.get(
          Uri.parse('https://www.google.com'),
        ).timeout(Duration(seconds: 5));
        print('🌐 Internet connectivity: OK (${connectivityTest.statusCode})');
      } catch (e) {
        print('❌ Internet connectivity issue: $e');
        throw Exception('No internet connection available');
      }

      // Step 0.5: Test backend health check
      try {
        final healthCheck = await http.get(
          Uri.parse('https://healthcheck-5yw2qsexaa-uc.a.run.app'),
        ).timeout(Duration(seconds: 10));
        print('🏥 Backend health check: ${healthCheck.statusCode}');
        print('🏥 Backend health check response body: ${healthCheck.body}');
        if (healthCheck.statusCode != 200) {
          throw Exception('Backend health check failed: ${healthCheck.statusCode}');
        }
      } catch (e) {
        print('❌ Backend health check failed: $e');
        throw Exception('Backend service is not available');
      }

      // Step 1: Generate custom credentials
      final credentials = await _generateCustomCredentials();
      print('🔑 Generated credentials: ${credentials.keys.join(', ')}');

      // Step 2: Prepare request body
      final requestBody = {
        'credentials': credentials,
      };

      print('📤 Request body: ${jsonEncode(requestBody)}');

      // Step 3: Send request to backend
      final response = await http.post(
        Uri.parse(_backendUrl),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode(requestBody),
      ).timeout(
        Duration(seconds: 60),
        onTimeout: () {
          throw Exception('Backend request timed out after 60 seconds');
        },
      );

      print('📥 Response status: ${response.statusCode}');
      print('📥 Response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        if (data['success'] == true && data['appCheckToken'] != null) {
          final token = data['appCheckToken'] as String;
          final expiresAt = data['expiresAt'] as String?;

          print('✅ App Check token received from backend');
          print('📱 Token length: ${token.length}');
          print('⏰ Expires at: $expiresAt');

          return token;
        } else {
          throw Exception('Backend returned invalid response: ${data['error'] ?? 'Unknown error'}');
        }
      } else {
        String errorMessage;
        try {
          final errorData = jsonDecode(response.body);
          errorMessage = 'Backend error (${response.statusCode}): ${errorData['error'] ?? 'Unknown error'}';
        } catch (e) {
          errorMessage = 'Backend error (${response.statusCode}): ${response.body}';
        }
        throw Exception(errorMessage);
      }
    } catch (e) {
      print('❌ Error requesting App Check token from backend: $e');
      rethrow;
    }
  }

  /// Get device information for credentials
  static Future<String> _getDeviceInfo() async {
    try {
      final deviceInfoPlugin = DeviceInfoPlugin();

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfoPlugin.androidInfo;
        return '${androidInfo.manufacturer}-${androidInfo.model}-${androidInfo.device}:${androidInfo.version.release}:${androidInfo.version.sdkInt}';
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfoPlugin.iosInfo;
        return '${iosInfo.name}-${iosInfo.model}-${iosInfo.systemName}:${iosInfo.systemVersion}';
      } else {
        return 'unknown-device';
      }
    } catch (e) {
      print('❌ Error getting device info: $e');
      return 'device-info-error';
    }
  }

  /// Generate SHA-256 hash
  static String _generateSHA256(String input) {
    try {
      final bytes = utf8.encode(input);
      final digest = sha256.convert(bytes);
      return digest.toString();
    } catch (e) {
      print('❌ Error generating SHA-256: $e');
      return 'hash-error';
    }
  }

  /// Validate App Check token with backend
  static Future<bool> validateTokenWithBackend(String token) async {
    try {
      final response = await http.post(
        Uri.parse('https://validateappchecktoken-5yw2qsexaa-uc.a.run.app'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'token': token,
        }),
      ).timeout(Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['valid'] == true;
      } else {
        return false;
      }
    } catch (e) {
      print('❌ Error validating token with backend: $e');
      return false;
    }
  }

  @override
  Future<String> getToken() async {
    try {
      // Get a token from your backend
      final token = await requestAppCheckTokenFromBackend();
      
      // Validate the token before returning
      if (await validateTokenWithBackend(token)) {
        return token;
      } else {
        throw Exception('Token validation failed');
      }
    } catch (e) {
      print('❌ Error getting token from custom provider: $e');
      rethrow;
    }
  }
}
