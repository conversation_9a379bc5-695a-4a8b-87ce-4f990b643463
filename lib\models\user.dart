class User {
  final String name;
  final String wdId;
  final String mobileNumber;
  final String kyc;

  User({required this.name, required this.wdId, required this.mobileNumber, required this.kyc});

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'dsId': wdId,
      'mobileNumber': mobileNumber,
      'kyc': kyc,
    };
  }

  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      name: map['name'],
      wdId: map['dsId'],
      mobileNumber: map['mobileNumber'],
      kyc: map['kyc'],
    );
  }
}
