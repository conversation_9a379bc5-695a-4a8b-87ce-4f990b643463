import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:intl/intl.dart';

class ProfileScreen extends StatefulWidget {
  @override
  _ProfileScreenState createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  String _wdName = 'Loading...';
  String _wdCode = 'Loading...';
  String _mobileNumber = 'Loading...';
  String _kyc = 'Loading...';
  String _city = 'Loading...';
  String _state = 'Loading...';
  String _pincode = 'Loading...';
  Timestamp? _createdAt;
  bool _isLoading = true;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _fetchProfileData();
  }

  Future<void> _fetchProfileData() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      User? user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        // First try to find the user document by authUid field
        QuerySnapshot userQuery = await FirebaseFirestore.instance
            .collection('wdUsers')
            .where('authUid', isEqualTo: user.uid)
            .limit(1)
            .get();

        // If no document found with authUid, try to find by phone number
        if (userQuery.docs.isEmpty) {
          // Extract phone number from Firebase user
          String? phoneNumber = user.phoneNumber;
          if (phoneNumber != null && phoneNumber.startsWith('+91')) {
            // Remove the +91 prefix to match our stored format
            String mobileNumber = phoneNumber.substring(3);

            userQuery = await FirebaseFirestore.instance
                .collection('wdUsers')
                .where('mobileNumber', isEqualTo: mobileNumber)
                .limit(1)
                .get();
          }
        }

        if (userQuery.docs.isEmpty) {
          print('User document does not exist in wdUsers collection');
          setState(() {
            _wdName = 'User data not found';
            _wdCode = 'User data not found';
            _mobileNumber = 'User data not found';
            _kyc = 'User data not found';
            _city = 'User data not found';
            _state = 'User data not found';
            _pincode = 'User data not found';
            _isLoading = false;
            _hasError = true;
          });

          // Show a message to the user
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('User data not found. Please log out and log in again.'),
            behavior: SnackBarBehavior.floating,
          ));
          return;
        }

        // Get the first matching document
        DocumentSnapshot userDoc = userQuery.docs.first;

        // Update the authUid field if it's not set
        if (userDoc['authUid'] == null) {
          await FirebaseFirestore.instance
              .collection('wdUsers')
              .doc(userDoc.id)
              .update({'authUid': user.uid});
        }

        Map<String, dynamic> data = userDoc.data() as Map<String, dynamic>;

        setState(() {
          _wdName = data['name'] ?? 'N/A';
          _wdCode = data['wdId'] ?? 'N/A';
          _mobileNumber = data['mobileNumber'] ?? 'N/A';
          _kyc = data['kyc'] ?? 'N/A';
          _city = data['city'] ?? 'N/A';
          _state = data['state'] ?? 'N/A';
          _pincode = data['pincode'] ?? 'N/A';
          _createdAt = data['createdAt'] as Timestamp?;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error fetching profile data: $e');
      setState(() {
        _wdName = 'Error loading data';
        _wdCode = 'Error loading data';
        _mobileNumber = 'Error loading data';
        _kyc = 'Error loading data';
        _city = 'Error loading data';
        _state = 'Error loading data';
        _pincode = 'Error loading data';
        _isLoading = false;
        _hasError = true;
      });

      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Error loading profile data. Please try again later.'),
        behavior: SnackBarBehavior.floating,
      ));
    }
  }

  String _getFormattedDate() {
    if (_createdAt == null) return 'N/A';

    DateTime dateTime = _createdAt!.toDate();
    return DateFormat('dd MMM yyyy').format(dateTime);
  }

  Widget _buildKycStatusBadge() {
    Color badgeColor;
    IconData badgeIcon;
    String statusText;

    switch (_kyc.toLowerCase()) {
      case 'approved':
        badgeColor = Colors.green;
        badgeIcon = Icons.check_circle;
        statusText = 'Approved';
        break;
      case 'pending':
        badgeColor = Colors.orange;
        badgeIcon = Icons.pending;
        statusText = 'Pending';
        break;
      case 'rejected':
        badgeColor = Colors.red;
        badgeIcon = Icons.cancel;
        statusText = 'Rejected';
        break;
      default:
        badgeColor = Colors.grey;
        badgeIcon = Icons.help;
        statusText = _kyc;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: badgeColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: badgeColor, width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(badgeIcon, size: 16, color: badgeColor),
          SizedBox(width: 6),
          Text(
            statusText,
            style: TextStyle(
              color: badgeColor,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('My Profile'),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _fetchProfileData,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : _hasError
              ? _buildErrorView()
              : _buildProfileView(),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 80,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            'Failed to load profile data',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Please try again later',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _fetchProfileData,
            icon: Icon(Icons.refresh),
            label: Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileView() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Profile Header
          _buildProfileHeader(),

          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Basic Information Card
                _buildInfoCard(
                  title: 'Basic Information',
                  icon: Icons.person,
                  children: [
                    _buildProfileField('Distributor Code', _wdCode),
                    _buildProfileField('Mobile Number', _mobileNumber),
                    _buildProfileField('Member Since', _getFormattedDate()),
                  ],
                ),

                SizedBox(height: 16),

                // Location Information Card
                _buildInfoCard(
                  title: 'Location Information',
                  icon: Icons.location_on,
                  children: [
                    _buildProfileField('City', _city),
                    _buildProfileField('State', _state),
                    _buildProfileField('Pincode', _pincode),
                  ],
                ),

                SizedBox(height: 16),

                // KYC Status Card
                _buildInfoCard(
                  title: 'KYC Status',
                  icon: Icons.verified_user,
                  trailing: _buildKycStatusBadge(),
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: Text(
                        _kyc.toLowerCase() == 'approved'
                            ? 'Your KYC verification is complete.'
                            : _kyc.toLowerCase() == 'pending'
                                ? 'Your KYC verification is in progress. We will notify you once it\'s approved.'
                                : _kyc.toLowerCase() == 'rejected'
                                    ? 'Your KYC verification was rejected. Please contact support for assistance.'
                                    : 'KYC status: $_kyc',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withOpacity(0.8),
          ],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            children: [
              // Profile Avatar
              CircleAvatar(
                radius: 50,
                backgroundColor: Colors.white,
                child: Text(
                  _wdName.isNotEmpty ? _wdName[0].toUpperCase() : '?',
                  style: TextStyle(
                    fontSize: 40,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ),
              SizedBox(height: 16),
              // Name
              Text(
                _wdName,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              SizedBox(height: 8),
              // WD Code
              Container(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  _wdCode,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
    Widget? trailing,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      icon,
                      color: Theme.of(context).primaryColor,
                      size: 20,
                    ),
                    SizedBox(width: 8),
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
                if (trailing != null) trailing,
              ],
            ),
            SizedBox(height: 8),
            Divider(),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildProfileField(String fieldName, String fieldValue) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              fieldName,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              fieldValue,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
