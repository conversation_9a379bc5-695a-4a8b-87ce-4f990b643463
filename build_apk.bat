@echo off
echo ===================================================
echo Building APK for ARM64 devices
echo ===================================================

echo Cleaning previous build...
flutter clean

echo Getting dependencies...
flutter pub get

echo Building APK...
flutter build apk --release --target-platform=android-arm64

echo Renaming APK...
copy build\app\outputs\flutter-apk\app-release.apk distributor.apk

echo ===================================================
echo Build completed. APK is available as distributor.apk
echo ===================================================
