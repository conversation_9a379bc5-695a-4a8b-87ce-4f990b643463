import 'dart:async';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:flutter/foundation.dart' show SynchronousFuture;

import 'app_localizations_en.dart';
import 'app_localizations_hi.dart';

class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  // Helper method to keep the code in the widgets concise
  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  // Static member to have a simple access to the delegate from the MaterialApp
  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  late Map<String, String> _localizedStrings;

  Future<bool> load() async {
    // Load the language JSON file from the "lang" folder
    switch (locale.languageCode) {
      case 'en':
        _localizedStrings = enValues;
        break;
      case 'hi':
        _localizedStrings = hiValues;
        break;
      default:
        _localizedStrings = enValues;
    }
    return true;
  }

  // This method will be called from every widget which needs a localized text
  String translate(String key) {
    return _localizedStrings[key] ?? key;
  }

  // Get current locale
  String get currentLanguage => locale.languageCode;

  // Check if current locale is RTL
  bool get isRtl => false; // Hindi is not RTL
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  // Include all supported language codes here
  @override
  bool isSupported(Locale locale) {
    return ['en', 'hi'].contains(locale.languageCode);
  }

  // Load the JSON file from the "lang" folder
  @override
  Future<AppLocalizations> load(Locale locale) {
    AppLocalizations localizations = AppLocalizations(locale);
    return localizations.load().then((_) => localizations);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
