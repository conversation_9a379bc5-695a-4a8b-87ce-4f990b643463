@echo off
echo ===================================================
echo Building ARM64 APK
echo ===================================================

echo Cleaning previous build...
flutter clean

echo Getting dependencies...
flutter pub get

echo Building ARM64 APK...
flutter build apk --release --target-platform=android-arm64 --split-debug-info=build/debug-info

echo Copying APK to root directory...
copy build\app\outputs\flutter-apk\app-release.apk wd-panel.apk

echo ===================================================
echo Build completed. APK is available as wd-panel.apk
echo ===================================================
