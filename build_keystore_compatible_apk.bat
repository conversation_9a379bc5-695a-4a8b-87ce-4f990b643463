@echo off
echo ===================================================
echo Building Keystore-Compatible APK
echo ===================================================

echo Cleaning previous build...
flutter clean

echo Getting dependencies...
flutter pub get

echo Building APK with keystore compatibility options...
flutter build apk --release --no-shrink --no-tree-shake-icons

echo Copying APK to root directory...
copy build\app\outputs\flutter-apk\app-release.apk wd-panel-keystore-compatible.apk

echo ===================================================
echo Build completed. APK is available as wd-panel-keystore-compatible.apk
echo ===================================================
