@echo off
echo ===== OPTIMIZED APK BUILD SCRIPT =====

echo Step 1: Cleaning previous builds...
flutter clean

echo Step 2: Getting dependencies...
flutter pub get

echo Step 3: Optimizing images...
call optimize_images.bat

echo Step 4: Building optimized APK...
echo Note: If build fails with missing R8 rules, we'll handle that automatically

:: First attempt to build with split ABIs
flutter build apk --split-per-abi --release --obfuscate --split-debug-info=build/app/outputs/symbols --target-platform android-arm,android-arm64 --tree-shake-icons

:: Check if missing_rules.txt was generated
if exist "build\app\outputs\mapping\release\missing_rules.txt" (
    echo Found missing R8 rules, adding them to proguard-rules.pro...
    type "build\app\outputs\mapping\release\missing_rules.txt" >> "android\app\proguard-rules.pro"

    echo Retrying build with updated rules...
    flutter build apk --split-per-abi --release --obfuscate --split-debug-info=build/app/outputs/symbols --target-platform android-arm,android-arm64 --tree-shake-icons
)

:: Rename the APKs with proper names
echo Renaming APKs with proper names...
if exist "build\app\outputs\flutter-apk\app-armeabi-v7a-release.apk" (
    copy "build\app\outputs\flutter-apk\app-armeabi-v7a-release.apk" "build\app\outputs\flutter-apk\distributor-arm32.apk"
    echo ARM32 APK renamed successfully!
)

if exist "build\app\outputs\flutter-apk\app-arm64-v8a-release.apk" (
    copy "build\app\outputs\flutter-apk\app-arm64-v8a-release.apk" "build\app\outputs\flutter-apk\distributor-arm64.apk"
    echo ARM64 APK renamed successfully!
)

echo Step 5: Analyzing APK sizes...
echo APK size before optimization was approximately 53MB

echo Analyzing ARM32 APK size:
for %%F in (build\app\outputs\flutter-apk\wd-panel-arm32.apk) do (
    echo ARM32 APK size: %%~zF bytes ^(approximately %%~zF / 1048576 MB^)
)

echo Analyzing ARM64 APK size:
for %%F in (build\app\outputs\flutter-apk\wd-panel-arm64.apk) do (
    echo ARM64 APK size: %%~zF bytes ^(approximately %%~zF / 1048576 MB^)
)

echo ===== BUILD COMPLETED SUCCESSFULLY =====
echo APKs are available at: build\app\outputs\flutter-apk\
echo - ARM32: wd-panel-arm32.apk
echo - ARM64: wd-panel-arm64.apk
echo.
