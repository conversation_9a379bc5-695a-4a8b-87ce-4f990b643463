import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

class NotificationService {
  static final FlutterLocalNotificationsPlugin _notificationsPlugin =
  FlutterLocalNotificationsPlugin();

  static void initialize() async {
    // Create notification channel for Android
    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      'high_importance_channel',
      'High Importance Notifications',
      importance: Importance.max,
      playSound: true,
    );

    // Initialize Flutter Local Notifications plugin
    await _notificationsPlugin.resolvePlatformSpecificImplementation<
        AndroidFlutterLocalNotificationsPlugin>()?.createNotificationChannel(channel);

    const InitializationSettings initializationSettings = InitializationSettings(
      android: AndroidInitializationSettings('@mipmap/ic_launcher'),
      iOS: DarwinInitializationSettings(),
    );

    await _notificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (details) async {
        // Handle notification tap
        if (details.payload != null) {
          // Navigate to specific screen based on payload
        }
      },
    );

    // Request permission for iOS
    await FirebaseMessaging.instance.requestPermission(
      alert: true,
      badge: true,
      sound: true,
    );

    // Handle foreground messages
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      RemoteNotification? notification = message.notification;
      AndroidNotification? android = message.notification?.android;

      if (notification != null && android != null) {
        _notificationsPlugin.show(
          notification.hashCode,
          notification.title,
          notification.body,
          NotificationDetails(
            android: AndroidNotificationDetails(
              'high_importance_channel',
              'High Importance Notifications',
              icon: '@mipmap/ic_launcher',
              importance: Importance.max,
              priority: Priority.high,
            ),
          ),
          payload: message.data.toString(),
        );

        // Save notification to Firestore
        saveNotificationToFirestore(message);
      }
    });

    // Handle notification open when app is in background or terminated
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      // Navigate to specific screen based on message

      // Save notification to Firestore
      saveNotificationToFirestore(message);
    });

    // Check for initial message (app opened from a notification)
    FirebaseMessaging.instance.getInitialMessage().then((RemoteMessage? message) {
      if (message != null) {
        // Save notification to Firestore
        saveNotificationToFirestore(message);
      }
    });
  }

  static Future<String?> getToken() async {
    return await FirebaseMessaging.instance.getToken();
  }

  static Future<void> subscribeToTopic(String topic) async {
    await FirebaseMessaging.instance.subscribeToTopic(topic);
  }

  static Future<void> unsubscribeFromTopic(String topic) async {
    await FirebaseMessaging.instance.unsubscribeFromTopic(topic);
  }

  // Create a direct notification without going through FCM
  static Future<void> createDirectNotification(String title, String message, {String type = 'direct'}) async {
    try {
      User? user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        print('Cannot create direct notification: No user logged in');
        return;
      }

      // First try to find the user document by authUid field
      QuerySnapshot userQuery = await FirebaseFirestore.instance
          .collection('wdUsers')
          .where('authUid', isEqualTo: user.uid)
          .limit(1)
          .get();

      // If no document found with authUid, try to find by phone number
      if (userQuery.docs.isEmpty) {
        // Extract phone number from Firebase user
        String? phoneNumber = user.phoneNumber;
        if (phoneNumber != null && phoneNumber.startsWith('+91')) {
          // Remove the +91 prefix to match our stored format
          String mobileNumber = phoneNumber.substring(3);

          userQuery = await FirebaseFirestore.instance
              .collection('wdUsers')
              .where('mobileNumber', isEqualTo: mobileNumber)
              .limit(1)
              .get();
        }
      }

      if (userQuery.docs.isEmpty) {
        print('Cannot create direct notification: User document not found');
        return;
      }

      // Get the first matching document
      DocumentSnapshot userDoc = userQuery.docs.first;

      String wdId = userDoc['wdId'];
      print('Creating direct notification for WD ID: $wdId');

      // Create notification document in our custom collection
      DocumentReference notificationRef = await FirebaseFirestore.instance.collection('wd_notifications').add({
        'title': title,
        'message': message,
        'timestamp': Timestamp.now(),
        'wdId': wdId,
        'isRead': false,
        'type': type,
        'data': {},
      });

      print('Direct notification created successfully with ID: ${notificationRef.id}');

      // Also show a local notification
      _notificationsPlugin.show(
        DateTime.now().millisecondsSinceEpoch.hashCode,
        title,
        message,
        NotificationDetails(
          android: AndroidNotificationDetails(
            'high_importance_channel',
            'High Importance Notifications',
            icon: '@mipmap/ic_launcher',
            importance: Importance.max,
            priority: Priority.high,
          ),
        ),
      );

      return;
    } catch (e) {
      print('Error creating direct notification: $e');
    }
  }

  static Future<void> saveNotificationToFirestore(RemoteMessage message) async {
    try {
      User? user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        print('Cannot save notification: No user logged in');
        return;
      }

      // First try to find the user document by authUid field
      QuerySnapshot userQuery = await FirebaseFirestore.instance
          .collection('wdUsers')
          .where('authUid', isEqualTo: user.uid)
          .limit(1)
          .get();

      // If no document found with authUid, try to find by phone number
      if (userQuery.docs.isEmpty) {
        // Extract phone number from Firebase user
        String? phoneNumber = user.phoneNumber;
        if (phoneNumber != null && phoneNumber.startsWith('+91')) {
          // Remove the +91 prefix to match our stored format
          String mobileNumber = phoneNumber.substring(3);

          userQuery = await FirebaseFirestore.instance
              .collection('wdUsers')
              .where('mobileNumber', isEqualTo: mobileNumber)
              .limit(1)
              .get();
        }
      }

      if (userQuery.docs.isEmpty) {
        print('Cannot save notification: User document not found');
        return;
      }

      // Get the first matching document
      DocumentSnapshot userDoc = userQuery.docs.first;

      // Update the authUid field if it's not set
      if (userDoc['authUid'] == null) {
        await FirebaseFirestore.instance
            .collection('wdUsers')
            .doc(userDoc.id)
            .update({'authUid': user.uid});
      }

      String wdId = userDoc['wdId'];
      print('Saving FCM notification for WD ID: $wdId');

      // Create notification document in our custom collection
      await FirebaseFirestore.instance.collection('wd_notifications').add({
        'title': message.notification?.title ?? 'New Notification',
        'message': message.notification?.body ?? '',
        'timestamp': Timestamp.now(),
        'wdId': wdId,
        'isRead': false,
        'type': message.data['type'] ?? 'fcm',
        'data': message.data,
      });

      print('FCM notification saved to wd_notifications collection successfully');
    } catch (e) {
      print('Error saving FCM notification to Firestore: $e');
    }
  }
}