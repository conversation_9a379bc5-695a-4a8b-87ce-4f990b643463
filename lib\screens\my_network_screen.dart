import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';

class MyNetworkScreen extends StatefulWidget {
  @override
  _MyNetworkScreenState createState() => _MyNetworkScreenState();
}

class _MyNetworkScreenState extends State<MyNetworkScreen> {
  String? _selectedDsName;
  String? _selectedBeat;

  List<String?> _dsNames = [];
  List<String?> _beats = [];
  List<Map<String, String>> _networkInfo = [];
  List<Map<String, String>> _filteredNetworkInfo = [];

  // Flag to track if the selected DS has no beats assigned
  bool _hasNoBeatsAssigned = false;

  // Controller for the search field
  TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _fetchDsNames();
    _fetchBeats();

    // Add listener to search controller
    _searchController.addListener(_filterNetworkInfo);
  }

  @override
  void dispose() {
    // Clean up the controller when the widget is disposed
    _searchController.dispose();
    super.dispose();
  }

  // Filter network info based on search query
  void _filterNetworkInfo() {
    String query = _searchController.text.toLowerCase();

    setState(() {
      if (query.isEmpty) {
        // If query is empty, show all results
        _filteredNetworkInfo = List.from(_networkInfo);
      } else {
        // Filter results based on query
        _filteredNetworkInfo = _networkInfo.where((item) {
          return item['outletName']!.toLowerCase().contains(query) ||
                 item['phone']!.toLowerCase().contains(query);
        }).toList();
      }
    });
  }

  // Map to store DS IDs by their display names
  Map<String, String> _dsIdMap = {};

  Future<void> _fetchDsNames() async {
    try {
      User? user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        // First try to find the user document by authUid field
        QuerySnapshot userQuery = await FirebaseFirestore.instance
            .collection('wdUsers')
            .where('authUid', isEqualTo: user.uid)
            .limit(1)
            .get();

        // If no document found with authUid, try to find by phone number
        if (userQuery.docs.isEmpty) {
          // Extract phone number from Firebase user
          String? phoneNumber = user.phoneNumber;
          if (phoneNumber != null && phoneNumber.startsWith('+91')) {
            // Remove the +91 prefix to match our stored format
            String mobileNumber = phoneNumber.substring(3);

            userQuery = await FirebaseFirestore.instance
                .collection('wdUsers')
                .where('mobileNumber', isEqualTo: mobileNumber)
                .limit(1)
                .get();
          }
        }

        if (userQuery.docs.isEmpty) {
          print('User document does not exist in wdUsers collection');
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('User data not found. Please log out and log in again.'),
          ));
          return;
        }

        // Get the first matching document
        DocumentSnapshot userDoc = userQuery.docs.first;

        // Update the authUid field if it's not set
        if (userDoc['authUid'] == null) {
          await FirebaseFirestore.instance
              .collection('wdUsers')
              .doc(userDoc.id)
              .update({'authUid': user.uid});
        }

        String wdCode = userDoc['wdId'];

        QuerySnapshot querySnapshot = await FirebaseFirestore.instance
            .collection('users')
            .where('wdCode', isEqualTo: wdCode)
            .get();

        setState(() {
          _dsIdMap.clear(); // Clear the map before adding new entries
          _dsNames = [null]; // Start with a null option for "All"

          // Create a list of unique display names
          List<String> displayNames = [];

          // Process each document
          for (var doc in querySnapshot.docs) {
            String name = doc['name'] as String;
            String dsId = doc['dsId'] as String;

            // Create a unique display name if needed
            String displayName = name;
            int suffix = 1;

            // If this name already exists, add a suffix
            while (displayNames.contains(displayName)) {
              displayName = '$name ($suffix)';
              suffix++;
            }

            displayNames.add(displayName);
            _dsIdMap[displayName] = dsId; // Store the mapping
          }

          // Add the display names to the dropdown list
          _dsNames.addAll(displayNames);
          _selectedDsName = null; // Reset selected value when fetching new data
        });
      }
    } catch (e) {
      print('Error fetching DS names: $e');
    }
  }

  Future<void> _fetchBeats({String? selectedDsName}) async {
    try {
      print('_fetchBeats called with selectedDsName: $selectedDsName');

      User? user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        // First try to find the user document by authUid field
        QuerySnapshot userQuery = await FirebaseFirestore.instance
            .collection('wdUsers')
            .where('authUid', isEqualTo: user.uid)
            .limit(1)
            .get();

        // If no document found with authUid, try to find by phone number
        if (userQuery.docs.isEmpty) {
          // Extract phone number from Firebase user
          String? phoneNumber = user.phoneNumber;
          if (phoneNumber != null && phoneNumber.startsWith('+91')) {
            // Remove the +91 prefix to match our stored format
            String mobileNumber = phoneNumber.substring(3);

            userQuery = await FirebaseFirestore.instance
                .collection('wdUsers')
                .where('mobileNumber', isEqualTo: mobileNumber)
                .limit(1)
                .get();
          }
        }

        if (userQuery.docs.isEmpty) {
          print('User document does not exist in wdUsers collection');
          return;
        }

        // Get the first matching document
        DocumentSnapshot userDoc = userQuery.docs.first;

        // Update the authUid field if it's not set
        if (userDoc['authUid'] == null) {
          await FirebaseFirestore.instance
              .collection('wdUsers')
              .doc(userDoc.id)
              .update({'authUid': user.uid});
        }

        String wdCode = userDoc['wdId'];
        print('WD Code: $wdCode');

        if (selectedDsName != null) {
          // Get the DS ID from the map using the display name
          String? dsId = _dsIdMap[selectedDsName];
          print('DS ID from map: $dsId for display name: $selectedDsName');
          print('Full DS ID map: $_dsIdMap');

          if (dsId != null) {
            print('Fetching beats for DS ID: $dsId');

            // First, get a sample beat to see its structure
            QuerySnapshot sampleBeats = await FirebaseFirestore.instance
                .collection('beats')
                .limit(1)
                .get();

            if (sampleBeats.docs.isNotEmpty) {
              print('Sample beat structure:');
              Map<String, dynamic> beatData = sampleBeats.docs.first.data() as Map<String, dynamic>;
              beatData.forEach((key, value) {
                print('  $key: $value');
              });
            }

            // Fetch all beats and filter manually - this is the most reliable approach
            QuerySnapshot allBeats = await FirebaseFirestore.instance
                .collection('beats')
                .get();

            print('Total beats found: ${allBeats.docs.length}');

            // Filter beats manually to find any that might be related to this DS
            List<DocumentSnapshot> filteredDocs = [];

            for (var doc in allBeats.docs) {
              Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
              bool isRelated = false;

              // Check each field for the DS ID or name
              data.forEach((key, value) {
                if (value != null) {
                  String valueStr = value.toString().toLowerCase();
                  String dsIdLower = dsId.toLowerCase();

                  // Check if this field contains the DS ID
                  if (valueStr == dsIdLower) {
                    print('Found exact match for DS ID in field $key with value $value');
                    isRelated = true;
                  }
                  // Check if this is a DS-related field
                  else if ((key.toLowerCase().contains('ds') ||
                           key.toLowerCase().contains('distributor') ||
                           key.toLowerCase().contains('salesman')) &&
                           (valueStr.contains(dsIdLower) || valueStr.contains(selectedDsName.toLowerCase()))) {
                    print('Found DS-related field $key with value $value that matches DS ID or name');
                    isRelated = true;
                  }
                }
              });

              if (isRelated) {
                filteredDocs.add(doc);

                // Print the beat that was found
                String beatName = doc['beatName'] as String? ??
                                 doc['beat_name'] as String? ??
                                 doc['name'] as String? ??
                                 'Unknown Beat';
                print('Found related beat: $beatName');
              }
            }

            print('Found ${filteredDocs.length} beats related to DS ID $dsId');

            // Use the filtered beats
            setState(() {
              _beats = [null]; // Start with a null option for "All"

              if (filteredDocs.isNotEmpty) {
                _beats.addAll(filteredDocs.map((doc) {
                  // Try to get beatName, fallback to other possible field names
                  String? beatName = doc['beatName'] as String?;
                  if (beatName == null) beatName = doc['beat_name'] as String?;
                  if (beatName == null) beatName = doc['name'] as String?;
                  if (beatName == null) beatName = 'Unknown Beat';
                  return beatName;
                }).toList());
                _hasNoBeatsAssigned = false; // DS has beats assigned
              } else {
                print('No beats found for this DS, showing "No Beat Assigned"');
                // If no beats found, add a special entry
                _beats = [null]; // Just the "All" option which will show as "No Beat Assigned"
                _hasNoBeatsAssigned = true; // DS has no beats assigned
              }

              _selectedBeat = null; // Reset selected value when fetching new data
            });
            return;
          } else {
            print('DS ID not found in map, fetching all beats');
            // If dsId is null, fetch all beats
            QuerySnapshot querySnapshot = await FirebaseFirestore.instance
                .collection('beats')
                .where('createdBy', isEqualTo: wdCode)
                .get();

            setState(() {
              _beats = [null]; // Start with a null option for "All"
              _beats.addAll(querySnapshot.docs.map((doc) {
                // Try to get beatName, fallback to other possible field names
                String? beatName = doc['beatName'] as String?;
                if (beatName == null) beatName = doc['beat_name'] as String?;
                if (beatName == null) beatName = doc['name'] as String?;
                if (beatName == null) beatName = 'Unknown Beat';
                return beatName;
              }).toList());
              _selectedBeat = null; // Reset selected value when fetching new data
              _hasNoBeatsAssigned = false; // Reset the flag since we're showing all beats
            });
          }
        } else {
          print('No DS selected, fetching all beats');
          // If no DS selected, fetch all beats
          QuerySnapshot querySnapshot = await FirebaseFirestore.instance
              .collection('beats')
              .where('createdBy', isEqualTo: wdCode)
              .get();

          setState(() {
            _beats = [null]; // Start with a null option for "All"
            _beats.addAll(querySnapshot.docs.map((doc) {
              // Try to get beatName, fallback to other possible field names
              String? beatName = doc['beatName'] as String?;
              if (beatName == null) beatName = doc['beat_name'] as String?;
              if (beatName == null) beatName = doc['name'] as String?;
              if (beatName == null) beatName = 'Unknown Beat';
              return beatName;
            }).toList());
            _selectedBeat = null; // Reset selected value when fetching new data
            _hasNoBeatsAssigned = false; // Reset the flag since we're showing all beats
          });
        }
      }
    } catch (e) {
      print('Error fetching beats: $e');
    }
  }

  Future<void> _fetchNetworkInfo() async {
    setState(() {
      _networkInfo.clear(); // Clear previous data before fetching new data
    });

    QuerySnapshot querySnapshot;

    User? user = FirebaseAuth.instance.currentUser;
    if (user == null) return;

    // First try to find the user document by authUid field
    QuerySnapshot userQuery = await FirebaseFirestore.instance
        .collection('wdUsers')
        .where('authUid', isEqualTo: user.uid)
        .limit(1)
        .get();

    // If no document found with authUid, try to find by phone number
    if (userQuery.docs.isEmpty) {
      // Extract phone number from Firebase user
      String? phoneNumber = user.phoneNumber;
      if (phoneNumber != null && phoneNumber.startsWith('+91')) {
        // Remove the +91 prefix to match our stored format
        String mobileNumber = phoneNumber.substring(3);

        userQuery = await FirebaseFirestore.instance
            .collection('wdUsers')
            .where('mobileNumber', isEqualTo: mobileNumber)
            .limit(1)
            .get();
      }
    }

    if (userQuery.docs.isEmpty) {
      print('User document does not exist in wdUsers collection');
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('User data not found. Please log out and log in again.'),
      ));
      return;
    }

    // Get the first matching document
    DocumentSnapshot userDoc = userQuery.docs.first;

    // Update the authUid field if it's not set
    if (userDoc['authUid'] == null) {
      await FirebaseFirestore.instance
          .collection('wdUsers')
          .doc(userDoc.id)
          .update({'authUid': user.uid});
    }

    String wdCode = userDoc['wdId'];

    QuerySnapshot dsQuerySnapshot = await FirebaseFirestore.instance
        .collection('users')
        .where('wdCode', isEqualTo: wdCode)
        .get();

    List<String> dsIds = dsQuerySnapshot.docs.map((doc) => doc['dsId'] as String).toList();

    if (_selectedDsName != null && _selectedBeat != null) {
      String? dsId = _dsIdMap[_selectedDsName]; // Get dsId from the map
      if (dsId != null) {
        // Fetch based on both DS Name and Beat
        querySnapshot = await FirebaseFirestore.instance
            .collection('outlets')
            .where('dsId', isEqualTo: dsId)
            .where('beat', isEqualTo: _selectedBeat)
            .get();
      } else {
        // Fallback if dsId is not found in the map
        querySnapshot = await FirebaseFirestore.instance
            .collection('outlets')
            .where('beat', isEqualTo: _selectedBeat)
            .get();
      }
    } else if (_selectedDsName != null) {
      String? dsId = _dsIdMap[_selectedDsName]; // Get dsId from the map
      if (dsId != null) {
        // Fetch based on DS Name only
        querySnapshot = await FirebaseFirestore.instance
            .collection('outlets')
            .where('dsId', isEqualTo: dsId)
            .get();
      } else {
        // Fallback if dsId is not found in the map
        querySnapshot = await FirebaseFirestore.instance
            .collection('outlets')
            .get();
      }
    } else if (_selectedBeat != null) {
      // Fetch based on Beat only
      querySnapshot = await FirebaseFirestore.instance
          .collection('outlets')
          .where('dsId', whereIn: dsIds)
          .where('beat', isEqualTo: _selectedBeat)
          .get();
    } else {
      // Fetch all outlets linked to all DS IDs under the current WD user
      querySnapshot = await FirebaseFirestore.instance
          .collection('outlets')
          .where('dsId', whereIn: dsIds)
          .get();
    }

    setState(() {
      _networkInfo = querySnapshot.docs.map((doc) {
        Map<String, dynamic> data = doc.data() as Map<String, dynamic>;

        // Get the phone number, with fallbacks for different field names
        String phone = '';
        if (data.containsKey('phone')) {
          phone = data['phone'] as String? ?? '';
        } else if (data.containsKey('phoneNumber')) {
          phone = data['phoneNumber'] as String? ?? '';
        } else if (data.containsKey('mobile')) {
          phone = data['mobile'] as String? ?? '';
        } else if (data.containsKey('mobileNumber')) {
          phone = data['mobileNumber'] as String? ?? '';
        } else if (data.containsKey('contact')) {
          phone = data['contact'] as String? ?? '';
        }

        return {
          'outletName': data['outletName'] as String? ?? 'Unknown Outlet',
          'phone': phone,
        };
      }).toList();

      // Initialize filtered list with all items
      _filteredNetworkInfo = List.from(_networkInfo);

      // Apply any existing search filter
      if (_searchController.text.isNotEmpty) {
        _filterNetworkInfo();
      }
    });
  }

  Future<void> _exportData() async {
    try {
      // Convert network data to CSV format
      String csvData = "Outlet Name,Phone\n";
      for (var entry in _filteredNetworkInfo) {
        // Escape commas in the outlet name
        String escapedName = entry['outletName']!.contains(',')
            ? '"${entry['outletName']}"'
            : entry['outletName']!;
        csvData += "$escapedName,${entry['phone']}\n";
      }

      // Get directory to save the file
      Directory? directory = await getExternalStorageDirectory();
      if (directory != null) {
        String timestamp = DateFormat('yyyyMMddHHmmss').format(DateTime.now());
        String fileName = 'network_info_$timestamp.csv';

        // Save the file
        File file = File('${directory.path}/$fileName');
        await file.writeAsString(csvData);

        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text('Data exported successfully to ${file.path}'),
          action: SnackBarAction(
            label: 'Rename',
            onPressed: () {
              _renameFile(file);
            },
          ),
        ));
      } else {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text('Failed to get storage directory.'),
        ));
      }
    } catch (e) {
      print('Error exporting data: $e');
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Error exporting data: $e'),
      ));
    }
  }

  Future<void> _renameFile(File file) async {
    TextEditingController _renameController = TextEditingController();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Rename File'),
        content: TextField(
          controller: _renameController,
          decoration: InputDecoration(labelText: 'New File Name'),
        ),
        actions: [
          TextButton(
            child: Text('Cancel'),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
          TextButton(
            child: Text('Rename'),
            onPressed: () async {
              String newFileName = _renameController.text.trim();
              if (newFileName.isNotEmpty) {
                String newPath = file.parent.path + '/' + newFileName + '.csv';
                await file.rename(newPath);
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                  content: Text('File renamed to $newFileName.csv'),
                ));
              }
            },
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('My Network'),
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).primaryColor.withOpacity(0.05),
              Colors.white,
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Filter Section
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.2),
                          spreadRadius: 1,
                          blurRadius: 3,
                          offset: Offset(0, 1),
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              'Filter Options',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                            ),
                            SizedBox(height: 16),
                            _buildDropdownField('Salesman Name', _selectedDsName, _dsNames, (value) {
                              setState(() {
                                _selectedDsName = value;
                                // Fetch beats for the selected DS
                                _fetchBeats(selectedDsName: value);
                                _fetchNetworkInfo(); // Fetch network info when DS Name changes
                              });
                            }),
                            _buildDropdownField('Beat', _selectedBeat, _beats, (value) {
                              setState(() {
                                _selectedBeat = value;
                                _fetchNetworkInfo(); // Fetch network info when Beat changes
                              });
                            }),
                          ],
                        ),
                      ),
                    ),
                  ),

                  SizedBox(height: 20),

                  // Results Section
                  Container(
                    height: 400, // Fixed height for results section
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.2),
                          spreadRadius: 1,
                          blurRadius: 3,
                          offset: Offset(0, 1),
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  Text(
                                    'Outlet List',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black,
                                    ),
                                  ),
                                  SizedBox(width: 10),
                                  Container(
                                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                    decoration: BoxDecoration(
                                      color: Theme.of(context).primaryColor.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Text(
                                      '${_filteredNetworkInfo.length} Outlets',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                        color: Theme.of(context).primaryColor,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              ElevatedButton.icon(
                                onPressed: _filteredNetworkInfo.isEmpty ? null : _exportData,
                                icon: Icon(Icons.download, size: 18),
                                label: Text('Export'),
                                style: ElevatedButton.styleFrom(
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                  // Disable button visually when no data is available
                                  backgroundColor: _filteredNetworkInfo.isEmpty
                                      ? Colors.grey[300]
                                      : null,
                                  foregroundColor: _filteredNetworkInfo.isEmpty
                                      ? Colors.grey[600]
                                      : null,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 16),

                          // Search Bar
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.grey[100],
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.grey[300]!),
                            ),
                            padding: EdgeInsets.symmetric(horizontal: 12),
                            child: TextField(
                              controller: _searchController,
                              decoration: InputDecoration(
                                hintText: 'Search by outlet name or phone',
                                border: InputBorder.none,
                                icon: Icon(Icons.search, color: Colors.grey[600]),
                                suffixIcon: _searchController.text.isNotEmpty
                                    ? IconButton(
                                        icon: Icon(Icons.clear, color: Colors.grey[600]),
                                        onPressed: () {
                                          _searchController.clear();
                                        },
                                      )
                                    : null,
                              ),
                            ),
                          ),

                          SizedBox(height: 16),

                          // Table Header
                          Container(
                            padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  flex: 3,
                                  child: Text(
                                    'Outlet Name',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black,
                                    ),
                                  ),
                                ),
                                Expanded(
                                  flex: 2,
                                  child: Text(
                                    'Phone',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),

                          SizedBox(height: 8),

                          // Table Body
                          Expanded(
                            child: _filteredNetworkInfo.isEmpty
                                ? Center(
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.search_off,
                                          size: 48,
                                          color: Colors.grey,
                                        ),
                                        SizedBox(height: 16),
                                        Text(
                                          _searchController.text.isNotEmpty
                                              ? 'No results found for "${_searchController.text}"'
                                              : 'No outlets found',
                                          style: TextStyle(
                                            fontSize: 16,
                                            color: Colors.grey,
                                          ),
                                        ),
                                        SizedBox(height: 8),
                                        Text(
                                          _searchController.text.isNotEmpty
                                              ? 'Try a different search term'
                                              : 'Try changing your filter options',
                                          style: TextStyle(
                                            fontSize: 14,
                                            color: Colors.grey,
                                          ),
                                        ),
                                      ],
                                    ),
                                  )
                                : ListView.builder(
                                    itemCount: _filteredNetworkInfo.length,
                                    itemBuilder: (context, index) {
                                      return Card(
                                        elevation: 0,
                                        margin: EdgeInsets.only(bottom: 8),
                                        color: index % 2 == 0
                                            ? Colors.grey.withOpacity(0.05)
                                            : Colors.white,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(8),
                                        ),
                                        child: Padding(
                                          padding: const EdgeInsets.all(12.0),
                                          child: Row(
                                            children: [
                                              Expanded(
                                                flex: 3,
                                                child: Text(
                                                  _filteredNetworkInfo[index]['outletName']!,
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                              ),
                                              Expanded(
                                                flex: 2,
                                                child: Text(
                                                  _filteredNetworkInfo[index]['phone']!,
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDropdownField(String label, String? selectedValue, List<String?> items, ValueChanged<String?> onChanged) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.grey[700],
            ),
          ),
          SizedBox(height: 8),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
              color: Colors.white,
            ),
            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String?>(
                value: selectedValue,
                isExpanded: true,
                icon: Icon(Icons.arrow_drop_down, color: Theme.of(context).primaryColor),
                onChanged: onChanged,
                items: items.map((item) {
                  String displayText;

                  if (item == null) {
                    // For null item (the first option)
                    if (label == 'Beat' && _selectedDsName != null && _hasNoBeatsAssigned) {
                      // If a DS is selected and it has no beats assigned
                      displayText = 'No Beat Assigned';
                    } else {
                      displayText = 'All';
                    }
                  } else {
                    displayText = item;
                  }

                  return DropdownMenuItem(
                    value: item,
                    child: Text(
                      displayText,
                      style: TextStyle(
                        color: Colors.black87,
                        fontWeight: item == selectedValue ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                  );
                }).toList(),
                hint: Text(
                  label == 'Beat' && _selectedDsName != null && _hasNoBeatsAssigned
                      ? 'No Beat Assigned'
                      : 'Select $label',
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
