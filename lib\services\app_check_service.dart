import 'package:firebase_app_check/firebase_app_check.dart';
import 'dart:async';

class AppCheckService {
  static final AppCheckService _instance = AppCheckService._internal();
  factory AppCheckService() => _instance;
  AppCheckService._internal();

  Timer? _tokenRefreshTimer;
  String? _currentToken;

  /// Initialize App Check service with automatic token refresh
  Future<void> initialize() async {
    try {
      // Get initial token
      await _refreshToken();
      
      // Set up automatic token refresh every 30 minutes
      _tokenRefreshTimer = Timer.periodic(Duration(minutes: 30), (timer) {
        _refreshToken();
      });
      
      print('App Check Service initialized successfully');
    } catch (e) {
      print('Error initializing App Check Service: $e');
    }
  }

  /// Refresh the App Check token
  Future<void> _refreshToken() async {
    try {
      String? token = await FirebaseAppCheck.instance.getToken();
      _currentToken = token;
      print('App Check token refreshed: ${token != null ? "Success" : "Failed"}');
    } catch (e) {
      print('Error refreshing App Check token: $e');
    }
  }

  /// Get the current App Check token
  Future<String?> getCurrentToken() async {
    try {
      // Try to get a fresh token
      String? token = await FirebaseAppCheck.instance.getToken();
      _currentToken = token;
      return token;
    } catch (e) {
      print('Error getting current App Check token: $e');
      return _currentToken; // Return cached token if available
    }
  }

  /// Ensure App Check token is valid before making Firebase requests
  Future<bool> ensureTokenValid() async {
    try {
      String? token = await getCurrentToken();
      if (token == null) {
        print('Warning: No App Check token available');
        return false;
      }
      print('App Check token is valid');
      return true;
    } catch (e) {
      print('Error validating App Check token: $e');
      return false;
    }
  }

  /// Force refresh the App Check token
  Future<void> forceRefresh() async {
    try {
      await _refreshToken();
      print('App Check token force refreshed');
    } catch (e) {
      print('Error force refreshing App Check token: $e');
    }
  }

  /// Dispose the service and clean up resources
  void dispose() {
    _tokenRefreshTimer?.cancel();
    _tokenRefreshTimer = null;
    _currentToken = null;
    print('App Check Service disposed');
  }

  /// Check if App Check is properly configured
  Future<bool> isConfigured() async {
    try {
      String? token = await FirebaseAppCheck.instance.getToken();
      return token != null;
    } catch (e) {
      print('App Check is not properly configured: $e');
      return false;
    }
  }

  /// Get debug information about App Check status
  Future<Map<String, dynamic>> getDebugInfo() async {
    try {
      String? token = await getCurrentToken();
      bool isConfigured = await this.isConfigured();
      
      return {
        'hasToken': token != null,
        'tokenLength': token?.length ?? 0,
        'isConfigured': isConfigured,
        'lastRefresh': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'hasToken': false,
        'tokenLength': 0,
        'isConfigured': false,
        'error': e.toString(),
      };
    }
  }
}
