import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:device_info_plus/device_info_plus.dart';

class OtpVerificationScreen extends StatefulWidget {
  final String mobileNumber;
  String verificationId;

  OtpVerificationScreen({
    Key? key,
    required this.mobileNumber,
    required this.verificationId,
  }) : super(key: key);

  @override
  _OtpVerificationScreenState createState() => _OtpVerificationScreenState();
}

class _OtpVerificationScreenState extends State<OtpVerificationScreen> {
  final _otpController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isVerifying = false;
  String? _deviceId;
  int? _resendToken; // Store the resend token to reduce reCAPTCHA frequency

  @override
  void initState() {
    super.initState();

    // Add listener to the main OTP controller
    _otpController.addListener(() {
      // Ensure only digits are entered
      final text = _otpController.text;
      if (text.isNotEmpty && !RegExp(r'^\d+$').hasMatch(text)) {
        _otpController.text = text.replaceAll(RegExp(r'[^\d]'), '');
      }

      // Limit to 6 digits
      if (text.length > 6) {
        _otpController.text = text.substring(0, 6);
        _otpController.selection = TextSelection.collapsed(offset: 6);
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (_deviceId == null) {
      _getDeviceId();
    }
  }

  @override
  void dispose() {
    // Remove listener before disposing
    _otpController.removeListener(() {});
    _otpController.dispose();
    super.dispose();
  }

  Future<void> _getDeviceId() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Theme.of(context).platform == TargetPlatform.android) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      setState(() {
        _deviceId = androidInfo.id;
      });
    } else if (Theme.of(context).platform == TargetPlatform.iOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      setState(() {
        _deviceId = iosInfo.identifierForVendor;
      });
    }
  }

  void _verifyOtp() async {
    if (_isVerifying) return;

    // Get OTP from the controller
    String otp = _otpController.text;

    if (otp.length != 6) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Please enter a valid 6-digit OTP')),
      );
      return;
    }

    setState(() {
      _isVerifying = true;
    });

    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => Center(
          child: CircularProgressIndicator(),
        ),
      );

      // Create credential with the verification ID and OTP
      PhoneAuthCredential credential = PhoneAuthProvider.credential(
        verificationId: widget.verificationId,
        smsCode: otp,
      );

      // Sign in with the credential
      UserCredential userCredential = await FirebaseAuth.instance.signInWithCredential(credential);

      // Check if user exists in Firestore by mobile number
      QuerySnapshot userQuery = await FirebaseFirestore.instance
          .collection('wdUsers')
          .where('mobileNumber', isEqualTo: widget.mobileNumber)
          .get();

      if (userQuery.docs.isEmpty) {
        // Close loading indicator
        Navigator.pop(context);

        // Sign out if user document doesn't exist
        await FirebaseAuth.instance.signOut();
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text('User data not found. Please sign up first.'),
        ));
        return;
      }

      // Get the first matching document
      DocumentSnapshot userDoc = userQuery.docs.first;

      // Close loading indicator
      Navigator.pop(context);

      // Get the user data
      Map<String, dynamic> userData = userDoc.data() as Map<String, dynamic>;

      // Check KYC status
      String kycStatus = userData['kyc'] ?? 'pending';
      if (kycStatus != 'approved') {
        // Sign out if KYC is not approved
        await FirebaseAuth.instance.signOut();
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text('KYC is pending, please contact Quickk support team'),
        ));
        return;
      }

      // Check account status
      String accountStatus = userData['status'] ?? 'active';
      if (accountStatus != 'active') {
        // Sign out if account is not active
        await FirebaseAuth.instance.signOut();
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text('Your account is inactive. Please contact Quickk support team for assistance.'),
        ));
        return;
      }

      // Check device ID
      String? currentDeviceId = userData['deviceId'];
      if (currentDeviceId != null && currentDeviceId != _deviceId) {
        // Sign out if device ID doesn't match
        await FirebaseAuth.instance.signOut();
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text('You are already logged in on another device. Please log out from that device first.'),
        ));
        return;
      }

      // Show loading indicator again
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => Center(
          child: CircularProgressIndicator(),
        ),
      );

      // Update device ID and auth UID in Firestore
      await FirebaseFirestore.instance
          .collection('wdUsers')
          .doc(userDoc.id)
          .update({
            'deviceId': _deviceId,
            'authUid': userCredential.user!.uid, // Store the authenticated user's UID
          });

      // Close loading indicator
      Navigator.pop(context);

      // Navigate to dashboard
      Navigator.pushNamedAndRemoveUntil(context, '/dashboard', (route) => false);
    } catch (e) {
      // Close loading indicator if it's showing
      Navigator.of(context, rootNavigator: true).pop();

      print('Error verifying OTP: $e');
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Error verifying OTP: $e'),
      ));
    } finally {
      setState(() {
        _isVerifying = false;
      });
    }
  }

  void _resendOtp() async {
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => Center(
          child: CircularProgressIndicator(),
        ),
      );

      // Use the forceResendingToken if available to avoid showing reCAPTCHA again
      await FirebaseAuth.instance.verifyPhoneNumber(
        phoneNumber: '+91${widget.mobileNumber}',
        forceResendingToken: _resendToken, // This helps reduce reCAPTCHA frequency
        verificationCompleted: (PhoneAuthCredential credential) async {
          // Auto-verification completed (usually on Android)
          Navigator.pop(context); // Close loading indicator

          // Show loading indicator again for sign in
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) => Center(
              child: CircularProgressIndicator(),
            ),
          );

          try {
            UserCredential userCredential = await FirebaseAuth.instance.signInWithCredential(credential);

            // Check if user exists in Firestore by mobile number
            QuerySnapshot userQuery = await FirebaseFirestore.instance
                .collection('wdUsers')
                .where('mobileNumber', isEqualTo: widget.mobileNumber)
                .get();

            // Close loading indicator
            Navigator.pop(context);

            if (userQuery.docs.isEmpty) {
              // Sign out if user document doesn't exist
              await FirebaseAuth.instance.signOut();
              ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                content: Text('User data not found. Please sign up first.'),
              ));
              return;
            }

            // Get the first matching document
            DocumentSnapshot userDoc = userQuery.docs.first;

            // Get the user data
            Map<String, dynamic> userData = userDoc.data() as Map<String, dynamic>;

            // Check KYC status
            String kycStatus = userData['kyc'] ?? 'pending';
            if (kycStatus != 'approved') {
              // Sign out if KYC is not approved
              await FirebaseAuth.instance.signOut();
              ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                content: Text('KYC is pending, please contact Quickk support team'),
              ));
              return;
            }

            // Check account status
            String accountStatus = userData['status'] ?? 'active';
            if (accountStatus != 'active') {
              // Sign out if account is not active
              await FirebaseAuth.instance.signOut();
              ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                content: Text('Your account is inactive. Please contact Quickk support team for assistance.'),
              ));
              return;
            }

            // Show loading indicator again
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) => Center(
                child: CircularProgressIndicator(),
              ),
            );

            // Update device ID and auth UID in Firestore
            await FirebaseFirestore.instance
                .collection('wdUsers')
                .doc(userDoc.id)
                .update({
                  'deviceId': _deviceId,
                  'authUid': userCredential.user!.uid, // Store the authenticated user's UID
                });

            // Close loading indicator
            Navigator.pop(context);

            // Navigate to dashboard
            Navigator.pushNamedAndRemoveUntil(context, '/dashboard', (route) => false);
          } catch (e) {
            // Close loading indicator
            Navigator.pop(context);

            print('Error in auto-verification: $e');
            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              content: Text('Error in auto-verification: $e'),
            ));
          }
        },
        verificationFailed: (FirebaseAuthException e) {
          // Close loading indicator
          Navigator.pop(context);

          print("Verification failed with error: ${e.message}");
          String errorMessage = 'Verification failed';

          // Check for specific error codes
          if (e.code == 'app-not-authorized' || e.code == 'operation-not-allowed') {
            errorMessage = 'Phone authentication requires Firebase billing to be enabled. Please contact the app administrator.';
          } else if (e.code == 'quota-exceeded') {
            errorMessage = 'SMS quota exceeded. Please try again later or contact support.';
          } else if (e.code == 'invalid-phone-number') {
            errorMessage = 'The phone number format is incorrect. Please enter a valid number.';
          } else if (e.message != null) {
            errorMessage = '${e.message}';
          }

          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text(errorMessage),
            duration: Duration(seconds: 5),
          ));
        },
        codeSent: (String newVerificationId, int? resendToken) {
          // Close loading indicator
          Navigator.pop(context);

          // Update the verification ID and resend token
          setState(() {
            widget.verificationId = newVerificationId;
            _resendToken = resendToken; // Store the token for future resends
            print('Updated verification ID: $newVerificationId');
            print('Updated resend token: $resendToken');

            // Clear the OTP field for the new code
            _otpController.clear();
          });

          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('OTP has been resent to your mobile number'),
          ));
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          // Auto-retrieval timeout
          print('Auto-retrieval timeout for verification ID: $verificationId');
        },
        timeout: Duration(seconds: 30), // Reduced timeout to minimize waiting
      );
    } catch (e) {
      // Close loading indicator if it's showing
      Navigator.of(context, rootNavigator: true).pop();

      print('Error resending OTP: $e');
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Error resending OTP: $e'),
      ));
    }
  }

  // Helper method to build status chips
  Widget _buildStatusChip({
    required String text,
    required Color color,
    required Color textColor,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: textColor,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('OTP Verification'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              SizedBox(height: 24),

              // Icon
              Icon(
                Icons.sms,
                size: 80,
                color: Theme.of(context).primaryColor,
              ),

              SizedBox(height: 24),

              // Title
              Text(
                'Verify Your Phone Number',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Colors.black),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: 16),

              // Subtitle
              Text(
                'We have sent a verification code to\n+91 ${widget.mobileNumber}',
                style: TextStyle(fontSize: 16, color: Colors.grey[700]),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: 32),

              // Single OTP Input Field
              Container(
                margin: EdgeInsets.symmetric(horizontal: 24),
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey[300]!),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 8,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Enter 6-digit OTP',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(height: 8),
                    TextField(
                      controller: _otpController,
                      keyboardType: TextInputType.number,
                      maxLength: 6,
                      autofocus: true,
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        letterSpacing: 8,
                      ),
                      decoration: InputDecoration(
                        hintText: "------",
                        hintStyle: TextStyle(
                          color: Colors.grey[400],
                          fontSize: 24,
                          letterSpacing: 8,
                        ),
                        counterText: "",
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(color: Colors.grey[300]!),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(color: Theme.of(context).primaryColor, width: 2),
                        ),
                        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                        prefixIcon: Icon(
                          Icons.lock_outline,
                          color: Theme.of(context).primaryColor,
                        ),
                        suffixIcon: _otpController.text.length == 6
                            ? Icon(
                                Icons.check_circle,
                                color: Colors.green,
                              )
                            : null,
                      ),
                      onChanged: (value) {
                        setState(() {});
                        if (value.length == 6) {
                          // Auto-submit when all 6 digits are entered
                          Future.delayed(Duration(milliseconds: 300), () {
                            _verifyOtp();
                          });
                        }
                      },
                    ),
                  ],
                ),
              ),

              SizedBox(height: 32),

              // Verify Button
              Container(
                margin: EdgeInsets.symmetric(horizontal: 24),
                height: 54,
                child: ElevatedButton(
                  onPressed: _isVerifying || _otpController.text.length != 6 ? null : _verifyOtp,
                  style: ElevatedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.0),
                    ),
                    backgroundColor: Theme.of(context).primaryColor,
                    disabledBackgroundColor: Colors.grey[300],
                    elevation: 2,
                  ),
                  child: _isVerifying
                    ? SizedBox(
                        height: 24,
                        width: 24,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.check_circle_outline,
                            color: Colors.white,
                          ),
                          SizedBox(width: 8),
                          Text(
                            'Verify OTP',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                ),
              ),

              SizedBox(height: 16),

              // Resend OTP
              Container(
                margin: EdgeInsets.symmetric(horizontal: 24),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "Didn't receive the code? ",
                      style: TextStyle(
                        color: Colors.grey[700],
                      ),
                    ),
                    TextButton(
                      onPressed: _resendOtp,
                      style: TextButton.styleFrom(
                        foregroundColor: Theme.of(context).primaryColor,
                        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      ),
                      child: Text(
                        'Resend OTP',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Additional information
              SizedBox(height: 24),
              Container(
                margin: EdgeInsets.symmetric(horizontal: 24),
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Colors.grey[700],
                      size: 20,
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Enter the 6-digit code sent to your mobile number for verification.',
                        style: TextStyle(
                          color: Colors.grey[700],
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
