package com.example.wd_panel

import android.os.Bundle
import io.flutter.embedding.android.FlutterActivity
import android.util.Log

class MainActivity : FlutterActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        // Disable keystore access
        try {
            System.setProperty("keystore.type", "NONE")
            System.setProperty("javax.net.ssl.trustStoreType", "NONE")
        } catch (e: Exception) {
            Log.e("MainActivity", "Error setting keystore properties: ${e.message}")
        }

        super.onCreate(savedInstanceState)

        // We're not initializing Firebase here to avoid keystore issues
        Log.d("MainActivity", "Firebase initialization skipped to avoid keystore issues")
    }
}
