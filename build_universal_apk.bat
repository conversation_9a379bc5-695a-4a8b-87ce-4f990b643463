@echo off
echo ===================================================
echo Building Universal APK (Compatible with most devices)
echo ===================================================

echo Cleaning previous build...
flutter clean

echo Getting dependencies...
flutter pub get

echo Building Universal APK...
flutter build apk --release

echo Copying APK to root directory...
copy build\app\outputs\flutter-apk\app-release.apk wd-panel-universal.apk

echo ===================================================
echo Build completed. APK is available as wd-panel-universal.apk
echo ===================================================
