import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/language_provider.dart';

class LanguageSelector extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final currentLanguage = languageProvider.locale.languageCode;

    return PopupMenuButton<String>(
      icon: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.language),
          SizedBox(width: 4),
          Text(
            currentLanguage == 'en' ? 'EN' : 'हिं',
            style: TextStyle(fontSize: 14),
          ),
        ],
      ),
      onSelected: (String languageCode) {
        try {
          // Use the direct method to avoid waiting for async operations
          languageProvider.setLocaleDirectly(languageCode);

          // Show a confirmation message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                languageCode == 'en'
                  ? 'Language changed to English'
                  : 'भाषा हिंदी में बदली गई',
              ),
              duration: Duration(seconds: 2),
            ),
          );
        } catch (e) {
          print('Error changing language: $e');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error changing language'),
              backgroundColor: Colors.red,
            ),
          );
        }
      },
      itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
        PopupMenuItem<String>(
          value: 'en',
          child: Row(
            children: [
              Text('English'),
              SizedBox(width: 8),
              if (currentLanguage == 'en') Icon(Icons.check, size: 18),
            ],
          ),
        ),
        PopupMenuItem<String>(
          value: 'hi',
          child: Row(
            children: [
              Text('हिंदी (Hindi)'),
              SizedBox(width: 8),
              if (currentLanguage == 'hi') Icon(Icons.check, size: 18),
            ],
          ),
        ),
      ],
    );
  }
}
