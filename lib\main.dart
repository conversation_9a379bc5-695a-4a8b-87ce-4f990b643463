import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:wd_panel/screens/dashboard_screen.dart';
import 'package:wd_panel/screens/welcome_screen.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:wd_panel/theme/app_theme.dart';
import 'package:wd_panel/services/notification_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'screens/signup_screen.dart';
import 'screens/login_screen.dart';
import 'screens/notifications_screen.dart';
import 'l10n/app_localizations.dart';
import 'providers/language_provider.dart';
import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:wd_panel/services/app_check_service.dart';
import 'package:wd_panel/services/custom_app_check_provider.dart';

// Global navigator key for accessing navigator from anywhere
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

// Initialize FlutterLocalNotificationsPlugin
final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
FlutterLocalNotificationsPlugin();

// This function must be top-level (not a class method)
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  print('Handling a background message ${message.messageId}');

  try {
    // We can't access the current user in the background handler,
    // so we'll save the notification with a special flag
    String? token = await FirebaseMessaging.instance.getToken();

    if (token != null) {
      // Find the user with this token
      QuerySnapshot userQuery = await FirebaseFirestore.instance
          .collection('wdUsers')
          .where('fcmToken', isEqualTo: token)
          .limit(1)
          .get();

      if (userQuery.docs.isNotEmpty) {
        String wdId = userQuery.docs.first['wdId'];

        // Save to our custom notifications collection
        await FirebaseFirestore.instance.collection('wd_notifications').add({
          'title': message.notification?.title ?? 'New Notification',
          'message': message.notification?.body ?? '',
          'timestamp': Timestamp.now(),
          'wdId': wdId,
          'isRead': false,
          'type': message.data['type'] ?? 'fcm_background',
          'data': message.data,
          'receivedInBackground': true
        });

        print('Background notification saved for WD ID: $wdId');
      }
    }
  } catch (e) {
    print('Error saving background notification: $e');
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();  await Firebase.initializeApp();  // Initialize App Check with custom provider
  
  // Initialize Firebase App Check with debug provider for development
  await FirebaseAppCheck.instance.activate(
    androidProvider: AndroidProvider.debug,  // Use debug provider for development
    appleProvider: AppleProvider.debug,      // Use debug provider for development
  );

  // Initialize App Check Service for custom token management
  await AppCheckService().initialize();

  // Set up background message handler
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  // Initialize Firebase Messaging
  FirebaseMessaging messaging = FirebaseMessaging.instance;

  // Request permission
  NotificationSettings settings = await messaging.requestPermission(
    alert: true,
    badge: true,
    sound: true,
  );

  // Initialize local notifications
  const AndroidInitializationSettings initializationSettingsAndroid =
  AndroidInitializationSettings('@mipmap/ic_launcher');

  final InitializationSettings initializationSettings = InitializationSettings(
    android: initializationSettingsAndroid,
  );

  await flutterLocalNotificationsPlugin.initialize(
    initializationSettings,
    onDidReceiveNotificationResponse: (NotificationResponse response) {
      // Handle notification tap
      print('Notification tapped: ${response.payload}');

      // Navigate to notifications screen when app is open and notification is tapped
      if (navigatorKey.currentState != null) {
        navigatorKey.currentState!.pushNamed('/notifications');
      }
    },
  );

  // Store FCM token in user document when available
  _storeFcmToken();

  // Handle foreground messages
  FirebaseMessaging.onMessage.listen((RemoteMessage message) {
    RemoteNotification? notification = message.notification;
    AndroidNotification? android = message.notification?.android;

    if (notification != null && android != null) {
      // Show the notification
      flutterLocalNotificationsPlugin.show(
        notification.hashCode,
        notification.title,
        notification.body,
        NotificationDetails(
          android: AndroidNotificationDetails(
            'default_channel_id',
            'Default Channel',
            importance: Importance.max,
            priority: Priority.high,
          ),
        ),
        payload: 'notifications', // Add payload for navigation
      );

      // Save the notification to Firestore
      NotificationService.saveNotificationToFirestore(message);
    }
  });

  // Initialize SharedPreferences before creating the app
  await SharedPreferences.getInstance().then((_) {
    print('SharedPreferences initialized successfully');
  }).catchError((error) {
    print('Error initializing SharedPreferences: $error');
  });

  // Create the language provider
  final languageProvider = LanguageProvider();

  runApp(
    ChangeNotifierProvider<LanguageProvider>.value(
      value: languageProvider,
      child: MyApp(),
    ),
  );
}

Future<void> _storeFcmToken() async {
  try {
    // Get the current user
    User? user = FirebaseAuth.instance.currentUser;
    if (user == null) return;

    // Get the FCM token
    String? token = await FirebaseMessaging.instance.getToken();
    if (token == null) return;

    // First try to find the user document by authUid field
    QuerySnapshot userQuery = await FirebaseFirestore.instance
        .collection('wdUsers')
        .where('authUid', isEqualTo: user.uid)
        .limit(1)
        .get();

    // If no document found with authUid, try to find by email (mobile number format)
    if (userQuery.docs.isEmpty) {
      // Extract mobile number from Firebase user email
      String? email = user.email;
      if (email != null && email.endsWith('@quickk.com')) {
        // Extract mobile number from email format (<EMAIL>)
        String mobileNumber = email.split('@')[0];

        userQuery = await FirebaseFirestore.instance
            .collection('wdUsers')
            .where('mobileNumber', isEqualTo: mobileNumber)
            .limit(1)
            .get();
      }
    }

    if (userQuery.docs.isEmpty) {
      print('Cannot store FCM token: User document not found');
      return;
    }

    // Get the first matching document
    DocumentSnapshot userDoc = userQuery.docs.first;

    // Update the authUid field if it's not set
    if (userDoc['authUid'] == null) {
      await FirebaseFirestore.instance
          .collection('wdUsers')
          .doc(userDoc.id)
          .update({'authUid': user.uid});
    }

    // Store the token in the user document
    await FirebaseFirestore.instance
        .collection('wdUsers')
        .doc(userDoc.id)
        .update({'fcmToken': token});

    print('FCM token stored for user: ${user.uid}');

    // Listen for token refreshes
    FirebaseMessaging.instance.onTokenRefresh.listen((newToken) {
      _updateFcmToken(newToken);
    });
  } catch (e) {
    print('Error storing FCM token: $e');
  }
}

Future<void> _updateFcmToken(String token) async {
  try {
    User? user = FirebaseAuth.instance.currentUser;
    if (user == null) return;

    // First try to find the user document by authUid field
    QuerySnapshot userQuery = await FirebaseFirestore.instance
        .collection('wdUsers')
        .where('authUid', isEqualTo: user.uid)
        .limit(1)
        .get();

    // If no document found with authUid, try to find by email (mobile number format)
    if (userQuery.docs.isEmpty) {
      // Extract mobile number from Firebase user email
      String? email = user.email;
      if (email != null && email.endsWith('@quickk.com')) {
        // Extract mobile number from email format (<EMAIL>)
        String mobileNumber = email.split('@')[0];

        userQuery = await FirebaseFirestore.instance
            .collection('wdUsers')
            .where('mobileNumber', isEqualTo: mobileNumber)
            .limit(1)
            .get();
      }
    }

    if (userQuery.docs.isEmpty) {
      print('Cannot update FCM token: User document not found');
      return;
    }

    // Get the first matching document
    DocumentSnapshot userDoc = userQuery.docs.first;

    // Update the token in the user document
    await FirebaseFirestore.instance
        .collection('wdUsers')
        .doc(userDoc.id)
        .update({'fcmToken': token});

    print('FCM token updated for user: ${user.uid}');
  } catch (e) {
    print('Error updating FCM token: $e');
  }
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    // Get the current locale from the provider
    final languageProvider = Provider.of<LanguageProvider>(context);

    // Set initial screen based on user authentication status
    Widget initialScreen = FirebaseAuth.instance.currentUser == null
        ? WelcomeScreen()
        : DashboardScreen();

    // If the language provider is still initializing, show a loading screen
    if (!languageProvider.initialized) {
      return MaterialApp(
        title: 'Distributor',
        theme: AppTheme.lightTheme,
        home: Scaffold(
          body: Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
    }

    return MaterialApp(
      title: 'Distributor',
      theme: AppTheme.lightTheme,
      navigatorKey: navigatorKey, // Add navigator key for global access
      home: initialScreen, // Use home instead of initialRoute

      // Localization settings
      locale: languageProvider.locale,
      supportedLocales: [Locale('en'), Locale('hi')],
      localizationsDelegates: [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],

      routes: {
        '/signup': (context) => SignUpScreen(),
        '/login': (context) => LoginScreen(),
        '/dashboard': (context) => DashboardScreen(),
        '/notifications': (context) => NotificationsScreen(),
        // We don't add the OTP verification screen here because it requires parameters
      },
    );
  }
}
