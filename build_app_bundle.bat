@echo off
echo ===== OPTIMIZED APP BUNDLE BUILD SCRIPT =====

echo Step 1: Cleaning previous builds...
flutter clean

echo Step 2: Getting dependencies...
flutter pub get

echo Step 3: Optimizing images...
call optimize_images.bat

echo Step 4: Building optimized App Bundle...
echo Note: App Bundle is the recommended format for Play Store publishing

:: Build the App Bundle
flutter build appbundle --release --obfuscate --split-debug-info=build/app/outputs/symbols --tree-shake-icons

:: Check if missing_rules.txt was generated
if exist "build\app\outputs\mapping\release\missing_rules.txt" (
    echo Found missing R8 rules, adding them to proguard-rules.pro...
    type "build\app\outputs\mapping\release\missing_rules.txt" >> "android\app\proguard-rules.pro"

    echo Retrying build with updated rules...
    flutter build appbundle --release --obfuscate --split-debug-info=build/app/outputs/symbols --tree-shake-icons
)

:: Rename the App Bundle
echo Renaming App Bundle...
if exist "build\app\outputs\bundle\release\app-release.aab" (
    copy "build\app\outputs\bundle\release\app-release.aab" "build\app\outputs\bundle\release\wd-panel.aab"
    echo App Bundle renamed successfully!
)

echo Step 5: Analyzing App Bundle size...
echo App Bundle size before optimization was approximately 53MB
for %%F in (build\app\outputs\bundle\release\wd-panel.aab) do (
    echo Current App Bundle size: %%~zF bytes ^(approximately %%~zF / 1048576 MB^)
)

echo ===== BUILD COMPLETED SUCCESSFULLY =====
echo App Bundle is available at: build\app\outputs\bundle\release\wd-panel.aab
echo.
echo Note: App Bundle is the recommended format for Play Store publishing.
echo It will generate optimized APKs for each device configuration.
echo.
