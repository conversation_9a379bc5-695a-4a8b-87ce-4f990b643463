# Firebase
-keep class com.google.firebase.** { *; }
-keep class com.google.android.play.integrity.** { *; }

# Integrity API
-keep class com.google.android.play.integrity.** { *; }

# Android Security and Keystore
-keep class android.security.** { *; }
-keep class javax.crypto.** { *; }
-keep class java.security.** { *; }
-keep class android.security.keystore.** { *; }

# Flutter
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}