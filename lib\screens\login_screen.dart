import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:device_info_plus/device_info_plus.dart';

class LoginScreen extends StatefulWidget {
  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _mobileNumberController = TextEditingController();
  final _passwordController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  String? _deviceId;

  bool _isLoggingIn = false; // Flag to prevent multiple login attempts
  bool _isResettingPassword = false; // Flag to prevent multiple password reset attempts
  bool _isPasswordVisible = false; // Flag for password visibility toggle

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (_deviceId == null) {
      _getDeviceId(); // Safe to call here
    }
  }

  Future<void> _getDeviceId() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Theme.of(context).platform == TargetPlatform.android) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      setState(() {
        _deviceId = androidInfo.id; // Use 'id' instead of 'androidId'
      });
    } else if (Theme.of(context).platform == TargetPlatform.iOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      setState(() {
        _deviceId = iosInfo.identifierForVendor; // Unique ID on iOS
      });
    }
  }


  void _login() async {
    // Prevent multiple login attempts
    if (_isLoggingIn) return;

    final mobileNumber = _mobileNumberController.text.trim();
    final password = _passwordController.text.trim();

    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoggingIn = true; // Set flag to prevent multiple attempts
      });

      try {
        // Show loading indicator
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => Center(
            child: CircularProgressIndicator(),
          ),
        );

        // First, check if the user exists in Firestore
        QuerySnapshot userQuery = await FirebaseFirestore.instance
            .collection('wdUsers')
            .where('mobileNumber', isEqualTo: mobileNumber)
            .get();

        if (userQuery.docs.isEmpty) {
          Navigator.pop(context); // Close loading indicator
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('User does not exist. Please sign up to log in.'),
          ));
          setState(() {
            _isLoggingIn = false;
          });
          return;
        }

        // Check KYC status and account status
        DocumentSnapshot userDoc = userQuery.docs.first;
        Map<String, dynamic> userData = userDoc.data() as Map<String, dynamic>;
        String kycStatus = userData['kyc'] ?? 'pending';
        String accountStatus = userData['status'] ?? 'active';
        String storedPassword = userData['password'] ?? '';

        if (kycStatus != 'approved') {
          Navigator.pop(context); // Close loading indicator
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('KYC is pending, please contact Quickk support team'),
          ));
          setState(() {
            _isLoggingIn = false;
          });
          return;
        }

        // Check if account is active
        if (accountStatus != 'active') {
          Navigator.pop(context); // Close loading indicator
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('Your account is inactive. Please contact Quickk support team for assistance.'),
          ));
          setState(() {
            _isLoggingIn = false;
          });
          return;
        }

        // Check if this is first-time login (password is blank)
        if (storedPassword.isEmpty) {
          // First-time login: set the entered password as the permanent password
          if (password.length < 6) {
            Navigator.pop(context); // Close loading indicator
            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              content: Text('Password must be at least 6 characters long.'),
            ));
            setState(() {
              _isLoggingIn = false;
            });
            return;
          }

          try {
            // Create Firebase Auth user with email/password
            String email = '$<EMAIL>';
            UserCredential userCredential = await FirebaseAuth.instance.createUserWithEmailAndPassword(
              email: email,
              password: password,
            );

            // Update password in Firestore
            await FirebaseFirestore.instance
                .collection('wdUsers')
                .doc(userDoc.id)
                .update({
                  'password': password,
                  'authUid': userCredential.user!.uid,
                  'deviceId': _deviceId,
                });

            // Close loading indicator
            Navigator.pop(context);

            // Show success message
            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              content: Text('Password set successfully! Welcome to the app.'),
              backgroundColor: Colors.green,
            ));

            // Navigate to dashboard
            Navigator.pushReplacementNamed(context, '/dashboard');
            return;

          } catch (e) {
            Navigator.pop(context); // Close loading indicator
            print('Error setting password: $e');
            String errorMessage = 'Error setting password. Please try again.';

            if (e is FirebaseAuthException) {
              switch (e.code) {
                case 'email-already-in-use':
                  errorMessage = 'This mobile number is already registered with a different account.';
                  break;
                case 'weak-password':
                  errorMessage = 'Password is too weak. Please choose a stronger password.';
                  break;
                default:
                  errorMessage = e.message ?? 'Error setting password. Please try again.';
              }
            }

            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              content: Text(errorMessage),
            ));
            setState(() {
              _isLoggingIn = false;
            });
            return;
          }
        }

        // Verify password for existing users
        if (password != storedPassword) {
          Navigator.pop(context); // Close loading indicator
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('Invalid password. Please try again.'),
          ));
          setState(() {
            _isLoggingIn = false;
          });
          return;
        }

        // Sign in with Firebase Auth using mobile number as email and password
        String email = '$<EMAIL>'; // Use mobile number as email
        UserCredential userCredential = await FirebaseAuth.instance.signInWithEmailAndPassword(
          email: email,
          password: password,
        );

        // Check device ID
        String? currentDeviceId = userData['deviceId'];
        if (currentDeviceId != null && currentDeviceId != _deviceId) {
          // Sign out if device ID doesn't match
          await FirebaseAuth.instance.signOut();
          Navigator.pop(context); // Close loading indicator
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('You are already logged in on another device. Please log out from that device first.'),
          ));
          setState(() {
            _isLoggingIn = false;
          });
          return;
        }

        // Update device ID and auth UID in Firestore
        await FirebaseFirestore.instance
            .collection('wdUsers')
            .doc(userDoc.id)
                  .update({
                    'deviceId': _deviceId,
                    'authUid': userCredential.user!.uid,
                  });

        // Close loading indicator
        Navigator.pop(context);

        // Navigate to dashboard
        Navigator.pushReplacementNamed(context, '/dashboard');

      } catch (e) {
        // Close loading indicator
        Navigator.pop(context);

        print('Login error: $e');
        String errorMessage = 'Login failed. Please try again.';

        if (e is FirebaseAuthException) {
          switch (e.code) {
            case 'user-not-found':
              errorMessage = 'No user found with this mobile number.';
              break;
            case 'wrong-password':
              errorMessage = 'Invalid password. Please try again.';
              break;
            case 'user-disabled':
              errorMessage = 'This account has been disabled.';
              break;
            case 'too-many-requests':
              errorMessage = 'Too many failed attempts. Please try again later.';
              break;
            default:
              errorMessage = e.message ?? 'Login failed. Please try again.';
          }
        }

        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text(errorMessage),
          duration: Duration(seconds: 5),
        ));

        setState(() {
          _isLoggingIn = false;
        });
      }
    }
  }




  void _sendSmsForPasswordReset() async {
    // Prevent multiple password reset attempts
    if (_isResettingPassword) return;

    final mobileNumber = _mobileNumberController.text.trim();

    if (mobileNumber.isNotEmpty) {
      setState(() {
        _isResettingPassword = true; // Set flag to prevent multiple attempts
      });

      try {
        // Show loading indicator
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => Center(
            child: CircularProgressIndicator(),
          ),
        );

        // Check if user exists in Firestore
        QuerySnapshot userQuery = await FirebaseFirestore.instance
            .collection('wdUsers')
            .where('mobileNumber', isEqualTo: mobileNumber)
            .get();

        // Close loading indicator
        Navigator.pop(context);

        if (userQuery.docs.isNotEmpty) {
          // Show options dialog for password reset
          _showPasswordResetOptionsDialog(userQuery.docs.first.id, mobileNumber);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('User does not exist. Please sign up to log in.'),
          ));
        }
      } catch (e) {
        // Close loading indicator if it's showing
        Navigator.of(context, rootNavigator: true).pop();

        print('Error in password reset: $e');
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text('Error in password reset: $e'),
        ));
      } finally {
        // Reset the flag regardless of success or failure
        setState(() {
          _isResettingPassword = false;
        });
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Please enter your mobile number to reset the password.'),
      ));
    }
  }

  void _showPasswordResetOptionsDialog(String userId, String mobileNumber) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Reset Password'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Choose a password reset method:'),
            SizedBox(height: 16),
            ListTile(
              leading: Icon(Icons.sms),
              title: Text('SMS Verification'),
              subtitle: Text('Receive OTP via SMS (requires billing)'),
              onTap: () {
                Navigator.pop(context);
                _sendSmsVerification(userId, mobileNumber);
              },
            ),
            Divider(),
            ListTile(
              leading: Icon(Icons.admin_panel_settings),
              title: Text('Admin Reset'),
              subtitle: Text('Reset to default password (mobile number)'),
              onTap: () {
                Navigator.pop(context);
                _resetToDefaultPassword(userId, mobileNumber);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _sendSmsVerification(String userId, String mobileNumber) async {
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => Center(
          child: CircularProgressIndicator(),
        ),
      );

      // For password reset, we still use OTP verification for security
      await FirebaseAuth.instance.verifyPhoneNumber(
        phoneNumber: '+91$mobileNumber',
        verificationCompleted: (PhoneAuthCredential credential) async {
          // Auto-verification completed (usually on Android)
          Navigator.pop(context); // Close loading indicator
          _resetPasswordAfterVerification(credential, userId);
        },
        verificationFailed: (FirebaseAuthException e) {
          // Close loading indicator
          Navigator.pop(context);

          print("Verification failed with error: ${e.message}");
          String errorMessage = 'Verification failed';

          // Check for specific error codes
          if (e.code == 'app-not-authorized' || e.code == 'operation-not-allowed') {
            errorMessage = 'Phone authentication requires Firebase billing to be enabled. Please contact the app administrator.';
          } else if (e.code == 'quota-exceeded') {
            errorMessage = 'SMS quota exceeded. Please try again later or contact support.';
          } else if (e.code == 'invalid-phone-number') {
            errorMessage = 'The phone number format is incorrect. Please enter a valid number.';
          } else if (e.message != null) {
            errorMessage = '${e.message}';
          }

          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text(errorMessage),
            duration: Duration(seconds: 5),
          ));
        },
        codeSent: (String verificationId, int? resendToken) {
          // Close loading indicator
          Navigator.pop(context);

          // Show reset password dialog
          _showResetPasswordDialog(verificationId, userId);
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          // Auto-retrieval timeout
        },
        timeout: Duration(seconds: 60),
      );
    } catch (e) {
      // Close loading indicator if it's showing
      Navigator.of(context, rootNavigator: true).pop();

      print('Error sending SMS: $e');
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Error sending SMS: $e'),
      ));
    }
  }

  void _resetToDefaultPassword(String userId, String mobileNumber) async {
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => Center(
          child: CircularProgressIndicator(),
        ),
      );

      // Reset password to the mobile number (as a default password)
      // First, get the user's email format
      String email = '$<EMAIL>';

      // Update password in Firebase Auth
      try {
        // Try to sign in with the email
        await FirebaseAuth.instance.signInWithEmailAndPassword(
          email: email,
          password: 'temporary_password_for_reset', // This will fail, but we need to try
        );
      } catch (signInError) {
        // Expected to fail, now we'll use sendPasswordResetEmail
        try {
          await FirebaseAuth.instance.sendPasswordResetEmail(email: email);
          // This might fail if email sending is not set up, so we'll also update Firestore
        } catch (resetError) {
          print('Email reset failed: $resetError');
          // Continue to Firestore update as fallback
        }
      }

      // Update password in Firestore as fallback
      await FirebaseFirestore.instance
          .collection('wdUsers')
          .doc(userId)
          .update({'password': mobileNumber});

      // Close loading indicator
      Navigator.pop(context);

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Password has been reset to your mobile number. Please login with your mobile number as password.'),
        duration: Duration(seconds: 5),
      ));
    } catch (e) {
      // Close loading indicator if it's showing
      Navigator.of(context, rootNavigator: true).pop();

      print('Error resetting password: $e');
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Error resetting password: $e'),
      ));
    }
  }

  void _showResetPasswordDialog(String verificationId, String userId) {
    final TextEditingController _codeController = TextEditingController();

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text('Verify Your Phone Number'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('We have sent a verification code to your phone'),
            SizedBox(height: 16),
            TextField(
              controller: _codeController,
              keyboardType: TextInputType.number,
              maxLength: 6,
              decoration: InputDecoration(
                labelText: 'Enter OTP',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final code = _codeController.text.trim();
              if (code.length == 6) {
                try {
                  PhoneAuthCredential credential = PhoneAuthProvider.credential(
                    verificationId: verificationId,
                    smsCode: code,
                  );

                  Navigator.pop(context);
                  await _resetPasswordAfterVerification(credential, userId);
                } catch (e) {
                  print('Error verifying code: $e');
                  ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                    content: Text('Error verifying code: $e'),
                  ));
                }
              } else {
                ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                  content: Text('Please enter a valid 6-digit OTP'),
                ));
              }
            },
            child: Text('Verify'),
          ),
        ],
      ),
    );
  }

  Future<void> _resetPasswordAfterVerification(PhoneAuthCredential credential, String userId) async {
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => Center(
          child: CircularProgressIndicator(),
        ),
      );

      // Sign in with the phone credential
      await FirebaseAuth.instance.signInWithCredential(credential);

      // Close loading indicator
      Navigator.pop(context);

      // After successful verification, show the dialog to enter new password
      _showNewPasswordDialog(userId);
    } catch (e) {
      // Close loading indicator if it's showing
      Navigator.of(context, rootNavigator: true).pop();

      print('Error in phone verification: $e');
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Error in phone verification: $e'),
      ));
    }
  }

  void _showNewPasswordDialog(String userId) {
    final TextEditingController _passwordController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Enter New Password'),
        content: TextField(
          controller: _passwordController,
          obscureText: true,
          decoration: InputDecoration(labelText: 'New Password'),
        ),
        actions: [
          TextButton(
            child: Text('Cancel'),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
          ElevatedButton(
            child: Text('Submit'),
            onPressed: () async {
              final newPassword = _passwordController.text.trim();
              if (newPassword.isNotEmpty) {
                try {
                  // Show loading indicator
                  showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (context) => Center(
                      child: CircularProgressIndicator(),
                    ),
                  );

                  // Get the current user
                  User? user = FirebaseAuth.instance.currentUser;

                  if (user != null) {
                    // Update the password in Firebase Auth
                    await user.updatePassword(newPassword);

                    // Sign out after the password reset
                    await FirebaseAuth.instance.signOut();

                    // Close loading indicator
                    Navigator.pop(context);

                    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                      content: Text('Password reset successfully! Please log in with your new password.'),
                    ));
                    Navigator.pop(context); // Close the password dialog
                  } else {
                    // Close loading indicator
                    Navigator.pop(context);

                    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                      content: Text('User not found. Please try again.'),
                    ));
                  }
                } catch (e) {
                  print('Error updating password: $e');
                  ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                    content: Text('Error updating password: $e'),
                  ));
                }
              } else {
                ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                  content: Text('Password cannot be empty.'),
                ));
              }
            },
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Log In'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // App Logo or Icon (optional)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 24.0),
                child: Icon(
                  Icons.account_circle,
                  size: 80,
                  color: Theme.of(context).primaryColor,
                ),
              ),

              // Welcome Text
              Padding(
                padding: const EdgeInsets.only(bottom: 24.0),
                child: Text(
                  'Welcome Back!',
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                  textAlign: TextAlign.center,
                ),
              ),

              // Mobile Number Field
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: TextFormField(
                  controller: _mobileNumberController,
                  decoration: InputDecoration(
                    labelText: 'Mobile Number',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.phone_android),
                    filled: true,
                    fillColor: Colors.grey[100],
                    hintText: 'Enter your 10-digit mobile number',
                  ),
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(10),
                  ],
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your mobile number';
                    } else if (value.length != 10) {
                      return 'Mobile number must be exactly 10 digits';
                    }
                    return null;
                  },
                ),
              ),

              // Password Field
              Padding(
                padding: const EdgeInsets.only(bottom: 24.0),
                child: TextFormField(
                  controller: _passwordController,
                  obscureText: !_isPasswordVisible,
                  decoration: InputDecoration(
                    labelText: 'Password',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.lock),
                    suffixIcon: IconButton(
                      icon: Icon(_isPasswordVisible ? Icons.visibility : Icons.visibility_off),
                      onPressed: () {
                        setState(() {
                          _isPasswordVisible = !_isPasswordVisible;
                        });
                      },
                    ),
                    filled: true,
                    fillColor: Colors.grey[100],
                    hintText: 'Enter your password',
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your password';
                    }
                    if (value.length < 6) {
                      return 'Password must be at least 6 characters long';
                    }
                    return null;
                  },
                ),
              ),

              // Information note for first-time users
              Padding(
                padding: const EdgeInsets.only(bottom: 24.0),
                child: Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue[200]!),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.blue[600], size: 20),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Note: If this is your first login, the password you enter will be set as your permanent password for future logins.',
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.blue[800],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Login Button
              SizedBox(
                height: 50,
                child: ElevatedButton(
                  onPressed: _isLoggingIn ? null : _login, // Disable button while logging in
                  style: ElevatedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                  ),
                  child: _isLoggingIn
                    ? SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        'Login',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                ),
              ),

              // Spacing between login button and sign up link
              SizedBox(height: 16.0),

              // Sign Up Link
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: TextButton(
                  onPressed: () {
                    Navigator.pushNamed(context, '/signup');
                  },
                  child: Text('New User? Sign up here.'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
