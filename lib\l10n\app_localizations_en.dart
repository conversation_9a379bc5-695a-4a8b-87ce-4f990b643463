// English Translations
final Map<String, String> enValues = {
  // Common
  'app_name': 'Distributor',
  'ok': 'OK',
  'cancel': 'Cancel',
  'save': 'Save',
  'delete': 'Delete',
  'edit': 'Edit',
  'loading': 'Loading...',
  'error': 'Error',
  'success': 'Success',
  'retry': 'Retry',
  'no_data': 'No data available',

  // Authentication
  'login': 'Login',
  'signup': 'Sign Up',
  'logout': 'Logout',
  'mobile_number': 'Mobile Number',
  'password': 'Password',
  'confirm_password': 'Confirm Password',
  'forgot_password': 'Forgot Password?',
  'reset_password': 'Reset Password',
  'enter_otp': 'Enter OTP',
  'verify': 'Verify',
  'login_failed': 'Login failed',
  'signup_failed': 'Sign up failed',
  'invalid_mobile': 'Invalid mobile number',
  'invalid_password': 'Invalid password',
  'passwords_dont_match': 'Passwords don\'t match',

  // Dashboard
  'dashboard': 'Dashboard',
  'welcome': 'Welcome',
  'my_network': 'My Network',
  'reports': 'Reports',
  'sales_register': 'Sales Register',
  'onboard_ds': 'Onboard Salesman',
  'create_beat': 'Create Beat',
  'profile': 'Profile',
  'notifications': 'Notifications',

  // My Network
  'select_ds': 'Select Salesman',
  'select_beat': 'Select Beat',
  'all': 'All',
  'outlets': 'Outlets',
  'number_of_outlets': '# of Outlets',
  'search': 'Search',
  'name': 'Name',
  'mobile': 'Mobile',

  // Sales Register
  'item': 'Item',
  'quantity': 'Quantity',
  'base': 'Base',
  'total': 'Total',
  'submit': 'Submit',
  'apple': 'Apple',
  'strawberry': 'Strawberry',
  'variance': 'Variance',
  'sales_submitted': 'Sales submitted successfully',

  // Reports
  'check_in_status': 'Check-in Status',
  'bill_cut': 'Bill Cut',
  'date': 'Date',
  'time': 'Time',
  'status': 'Status',
  'export': 'Export',

  // Onboard DS
  'ds_details': 'DS Details',
  'full_name': 'Full Name',
  'pincode': 'Pincode',
  'state': 'State',
  'region': 'Region',
  'city': 'City',
  'locality': 'Locality',
  'ed_code': 'ED Code',
  'wd_code': 'WD Code',
  'submit_details': 'Submit Details',

  // Notifications
  'no_notifications': 'No notifications yet',
  'today': 'Today',
  'yesterday': 'Yesterday',
  'mark_all_read': 'Mark all as read',

  // Profile
  'update_profile': 'Update Profile',
  'change_password': 'Change Password',
  'current_password': 'Current Password',
  'new_password': 'New Password',
  'update': 'Update',

  // Create Beat
  'beat_name': 'Beat Name',
  'beat_code': 'Beat Code',
  'beat_details': 'Beat Details',
  'add_beat': 'Add Beat',

  // Errors
  'connection_error': 'Connection error. Please check your internet connection.',
  'server_error': 'Server error. Please try again later.',
  'unknown_error': 'Unknown error occurred.',
  'required_field': 'This field is required',
  'session_expired': 'Your session has expired. Please login again.',
};
