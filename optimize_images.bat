@echo off
echo This script will optimize images for the app

echo Installing required packages...
flutter pub add flutter_native_splash --dev
flutter pub add flutter_launcher_icons --dev

echo Step 1: Optimizing launcher icons...
flutter pub run flutter_launcher_icons

echo Step 2: Creating optimized splash screen...
flutter pub run flutter_native_splash:create

echo Step 3: Compressing image assets...
:: Create a temporary directory for optimized images
if not exist "temp_optimized" mkdir temp_optimized

:: Check if pngquant is installed
where pngquant >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo pngquant not found. Installing via npm...
    npm install -g pngquant-bin
)

:: Optimize PNG images
for %%f in (assets\images\*.png) do (
    echo Optimizing %%f
    pngquant --force --quality=65-80 --skip-if-larger --strip "%%f" --output "temp_optimized\%%~nxf"
    if exist "temp_optimized\%%~nxf" copy /Y "temp_optimized\%%~nxf" "%%f"
)

:: Optimize JPEG images if available
where jpegtran >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    for %%f in (assets\images\*.jpg assets\images\*.jpeg) do (
        echo Optimizing %%f
        jpegtran -copy none -optimize -progressive -outfile "temp_optimized\%%~nxf" "%%f"
        if exist "temp_optimized\%%~nxf" copy /Y "temp_optimized\%%~nxf" "%%f"
    )
) else (
    echo jpegtran not found. JPEG optimization skipped.
)

:: Clean up
rmdir /S /Q temp_optimized

echo Step 4: Cleaning up packages...
flutter pub remove flutter_native_splash --dev

echo Image optimization completed!
