@echo off
echo ===== BUILDING APK WITHOUT TREE SHAKING ICONS =====

echo Step 1: Cleaning previous builds...
flutter clean

echo Step 2: Getting dependencies...
flutter pub get

echo Step 3: Building APK without tree shaking icons...
flutter build apk --release --no-tree-shake-icons --obfuscate --split-debug-info=build/app/outputs/symbols

:: Rename the APK to wd-panel.apk
echo Renaming APK to wd-panel.apk...
if exist "build\app\outputs\flutter-apk\app-release.apk" (
    copy "build\app\outputs\flutter-apk\app-release.apk" "build\app\outputs\flutter-apk\wd-panel.apk"
    echo APK renamed successfully!
)

echo ===== BUILD COMPLETED =====
echo APK is available at: build\app\outputs\flutter-apk\wd-panel.apk
echo.
