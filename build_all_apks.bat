@echo off
echo ===================================================
echo Building APKs for different architectures
echo ===================================================

echo Cleaning previous build...
flutter clean

echo Getting dependencies...
flutter pub get

echo ===================================================
echo Building ARM64 (64-bit) APK...
echo ===================================================
flutter build apk --release --target-platform=android-arm64 --split-debug-info=build/debug-info --tree-shake-icons

echo Copying ARM64 APK to root directory...
copy build\app\outputs\apk\release\app-arm64-v8a-release.apk wd-panel-arm64.apk

echo ===================================================
echo Building ARM (32-bit) APK...
echo ===================================================
flutter build apk --release --target-platform=android-arm --split-debug-info=build/debug-info --tree-shake-icons

echo Copying ARM APK to root directory...
copy build\app\outputs\apk\release\app-armeabi-v7a-release.apk wd-panel-arm.apk

echo ===================================================
echo Building Universal APK...
echo ===================================================
flutter build apk --release --split-debug-info=build/debug-info --tree-shake-icons

echo Copying Universal APK to root directory...
copy build\app\outputs\flutter-apk\app-release.apk wd-panel.apk

echo ===================================================
echo Build completed. The following APKs are available:
echo - distributor-arm64.apk (for 64-bit devices)
echo - distributor-arm.apk (for 32-bit devices)
echo - distributor.apk (universal)
echo ===================================================
