import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

class ReportsScreen extends StatefulWidget {
  @override
  _ReportsScreenState createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen> {
  List<Map<String, String>> _checkinStatus = [];
  List<Map<String, String>> _billCutSummary = [];
  bool showCheckinStatus = true;

  // Flags to track if we've attempted to load data
  bool _hasAttemptedLoadCheckin = false;
  bool _hasAttemptedLoadBillCut = false;

  @override
  void initState() {
    super.initState();
    _fetchCheckinStatus();
    _fetchBillCutSummary();
  }

  Future<void> _fetchCheckinStatus() async {
    try {
      User? user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        // First try to find the user document by authUid field
        QuerySnapshot userQuery = await FirebaseFirestore.instance
            .collection('wdUsers')
            .where('authUid', isEqualTo: user.uid)
            .limit(1)
            .get();

        // If no document found with authUid, try to find by phone number
        if (userQuery.docs.isEmpty) {
          // Extract phone number from Firebase user
          String? phoneNumber = user.phoneNumber;
          if (phoneNumber != null && phoneNumber.startsWith('+91')) {
            // Remove the +91 prefix to match our stored format
            String mobileNumber = phoneNumber.substring(3);

            userQuery = await FirebaseFirestore.instance
                .collection('wdUsers')
                .where('mobileNumber', isEqualTo: mobileNumber)
                .limit(1)
                .get();
          }
        }

        if (userQuery.docs.isEmpty) {
          print('User document does not exist in wdUsers collection');
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('User data not found. Please log out and log in again.'),
          ));
          return;
        }

        // Get the first matching document
        DocumentSnapshot userDoc = userQuery.docs.first;

        // Update the authUid field if it's not set
        if (userDoc['authUid'] == null) {
          await FirebaseFirestore.instance
              .collection('wdUsers')
              .doc(userDoc.id)
              .update({'authUid': user.uid});
        }

        String wdCode = userDoc['wdId'];

        QuerySnapshot dsQuerySnapshot = await FirebaseFirestore.instance
            .collection('users')
            .where('wdCode', isEqualTo: wdCode)
            .get();

        // Fetch today's date
        DateTime now = DateTime.now();

        // Create start and end timestamps for today (00:00 to 23:59)
        DateTime startOfDay = DateTime(now.year, now.month, now.day, 0, 0, 0);
        DateTime endOfDay = DateTime(now.year, now.month, now.day, 23, 59, 59);
        Timestamp startTimestamp = Timestamp.fromDate(startOfDay);
        Timestamp endTimestamp = Timestamp.fromDate(endOfDay);

        // Clear previous data
        setState(() {
          _checkinStatus.clear();
        });

        for (var dsDoc in dsQuerySnapshot.docs) {
          String dsName = dsDoc['name'];
          String dsId = dsDoc['dsId'];

          // Fetch all check-ins for this dsID and filter in the app
          QuerySnapshot checkinSnapshot = await FirebaseFirestore.instance
              .collection('checkins')
              .where('dsID', isEqualTo: dsId)
              .get();

          // Filter the results to only include today's check-ins
          List<QueryDocumentSnapshot> todayCheckins = checkinSnapshot.docs.where((doc) {
            Timestamp checkinTimestamp = doc['checkinTimestamp'];
            return checkinTimestamp.compareTo(startTimestamp) >= 0 &&
                   checkinTimestamp.compareTo(endTimestamp) <= 0;
          }).toList();

          if (todayCheckins.isNotEmpty) {
            for (var checkinDoc in todayCheckins) {
              Map<String, dynamic> checkinData = checkinDoc.data() as Map<String, dynamic>;
              Timestamp checkinTimestamp = checkinData['checkinTimestamp'];

              // Format the check-in time to show only hours and minutes
              String checkinTime = _formatTimestamp(checkinTimestamp);

              // Assume that checkoutTimestamp exists in the document
              Timestamp? checkoutTimestamp = checkinData.containsKey('checkoutTimestamp')
                  ? checkinData['checkoutTimestamp'] as Timestamp?
                  : null;

              String checkoutTime = checkoutTimestamp != null
                  ? _formatTimestamp(checkoutTimestamp)
                  : 'N/A';

              String hoursWorked = _formatHoursWorked(checkinTimestamp, checkoutTimestamp);

              setState(() {
                _checkinStatus.add({
                  'dsName': dsName,
                  'checkin': checkinTime,
                  'checkout': checkoutTime,
                  'hours': hoursWorked,
                });
              });
            }
          } else {
            // No check-in data for this dsID today
            setState(() {
              _checkinStatus.add({
                'dsName': dsName,
                'checkin': 'N/A',
                'checkout': 'N/A',
                'hours': '--:--',
              });
            });
          }
        }
      }
    } catch (e) {
      print('Error fetching check-in status: $e');
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Error loading check-in data. Please try again later.'),
      ));
    }
  }

  Future<void> _fetchBillCutSummary() async {
    try {
      User? user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        // First try to find the user document by authUid field
        QuerySnapshot userQuery = await FirebaseFirestore.instance
            .collection('wdUsers')
            .where('authUid', isEqualTo: user.uid)
            .limit(1)
            .get();

        // If no document found with authUid, try to find by phone number
        if (userQuery.docs.isEmpty) {
          // Extract phone number from Firebase user
          String? phoneNumber = user.phoneNumber;
          if (phoneNumber != null && phoneNumber.startsWith('+91')) {
            // Remove the +91 prefix to match our stored format
            String mobileNumber = phoneNumber.substring(3);

            userQuery = await FirebaseFirestore.instance
                .collection('wdUsers')
                .where('mobileNumber', isEqualTo: mobileNumber)
                .limit(1)
                .get();
          }
        }

        if (userQuery.docs.isEmpty) {
          print('User document does not exist in wdUsers collection');
          return;
        }

        // Get the first matching document
        DocumentSnapshot userDoc = userQuery.docs.first;

        // Update the authUid field if it's not set
        if (userDoc['authUid'] == null) {
          await FirebaseFirestore.instance
              .collection('wdUsers')
              .doc(userDoc.id)
              .update({'authUid': user.uid});
        }

        String wdCode = userDoc['wdId'];

        QuerySnapshot dsQuerySnapshot = await FirebaseFirestore.instance
            .collection('users')
            .where('wdCode', isEqualTo: wdCode)
            .get();

        DateTime now = DateTime.now();

        // Create start and end timestamps for today (00:00 to 23:59)
        DateTime startOfDay = DateTime(now.year, now.month, now.day, 0, 0, 0);
        DateTime endOfDay = DateTime(now.year, now.month, now.day, 23, 59, 59);
        Timestamp startTimestamp = Timestamp.fromDate(startOfDay);
        Timestamp endTimestamp = Timestamp.fromDate(endOfDay);

        // Clear previous data
        setState(() {
          _billCutSummary.clear();
        });

        for (var dsDoc in dsQuerySnapshot.docs) {
          String dsName = dsDoc['name'];
          String dsId = dsDoc['dsId'];

          // Fetch all orders for this dsID and filter in the app
          QuerySnapshot orderSnapshot = await FirebaseFirestore.instance
              .collection('orders')
              .where('dsID', isEqualTo: dsId)
              .get();

          // Filter the results to only include today's orders
          List<QueryDocumentSnapshot> todayOrders = orderSnapshot.docs.where((doc) {
            if (!doc.data().toString().contains('checkinTimestamp')) return false;
            Timestamp orderTimestamp = doc['checkinTimestamp'];
            return orderTimestamp.compareTo(startTimestamp) >= 0 &&
                   orderTimestamp.compareTo(endTimestamp) <= 0;
          }).toList();

          int billCount = todayOrders.length;

          setState(() {
            _billCutSummary.add({
              'dsName': dsName,
              'billCount': billCount.toString(),
            });
          });
        }
      }
    } catch (e) {
      print('Error fetching bill cut summary: $e');
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Error loading bill cut data. Please try again later.'),
      ));
    }
  }

  // Helper method to format timestamps to a readable time format
  String _formatTimestamp(Timestamp timestamp) {
    DateTime dateTime = timestamp.toDate();
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  // Helper method to format the current date for display
  String _formatCurrentDate() {
    DateTime now = DateTime.now();
    return '${now.day}/${now.month}/${now.year}';
  }

  // Helper method to format hours worked in HH:MM format
  String _formatHoursWorked(Timestamp checkinTimestamp, Timestamp? checkoutTimestamp) {
    if (checkoutTimestamp == null) return '--:--';

    // Calculate the difference in minutes
    int differenceInMinutes = checkoutTimestamp.toDate().difference(checkinTimestamp.toDate()).inMinutes;

    // Calculate hours and remaining minutes
    int hours = differenceInMinutes ~/ 60;
    int minutes = differenceInMinutes % 60;

    // Format as HH:MM
    return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}';
  }

  // Helper method to build status chips for N/A values
  Widget _buildStatusChip({
    required String text,
    required Color color,
    required Color textColor,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: textColor,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Reports'),
        elevation: 0,
      ),
      body: Column(
        children: [
          // Tab-like selector with improved styling
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).primaryColor.withOpacity(0.2),
                  width: 1,
                ),
              ),
            ),
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildTabButton(
                  title: 'Check In Status',
                  isSelected: showCheckinStatus,
                  onTap: () {
                    setState(() {
                      showCheckinStatus = true;
                    });
                  },
                ),
                SizedBox(width: 16),
                _buildTabButton(
                  title: 'Bill Cut Summary',
                  isSelected: !showCheckinStatus,
                  onTap: () {
                    setState(() {
                      showCheckinStatus = false;
                    });
                  },
                ),
              ],
            ),
          ),

          // Content area
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: showCheckinStatus
                ? _buildCheckinStatusView()
                : _buildBillCutSummaryView(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabButton({
    required String title,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? Theme.of(context).primaryColor : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          title,
          style: TextStyle(
            color: isSelected ? Colors.white : Theme.of(context).primaryColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildCheckinStatusView() {
    // Check if data is still loading or if there's truly no data
    if (_checkinStatus.isEmpty) {
      // If we've already attempted to load data, show "No data" message
      if (_hasAttemptedLoadCheckin) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.info_outline,
                size: 48,
                color: Colors.grey[400],
              ),
              SizedBox(height: 16),
              Text(
                'No data available',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[700],
                ),
              ),
              SizedBox(height: 8),
              Text(
                'There is no check-in data for today',
                style: TextStyle(
                  color: Colors.grey[600],
                ),
              ),
              SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () {
                  setState(() {
                    _hasAttemptedLoadCheckin = false; // Reset the flag
                  });
                  _fetchCheckinStatus(); // Retry loading data
                },
                icon: Icon(Icons.refresh),
                label: Text('Refresh'),
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
              ),
            ],
          ),
        );
      }

      // Mark that we've attempted to load data
      Future.delayed(Duration(seconds: 3), () {
        if (mounted && _checkinStatus.isEmpty) {
          setState(() {
            _hasAttemptedLoadCheckin = true;
          });
        }
      });

      // Show loading indicator for the first attempt
      return Center(child: CircularProgressIndicator());
    }

    return Column(
      children: [
        // Header with date
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Check-in Status',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Theme.of(context).primaryColor.withOpacity(0.2),
                ),
              ),
              child: Text(
                _formatCurrentDate(),
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 16),

        // Table Header with improved styling
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(8),
              topRight: Radius.circular(8),
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            child: Row(
              children: [
                Expanded(
                  flex: 4,
                  child: Text(
                    'Salesman',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Check-in',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Check-out',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'HH:MM',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
        ),

        // Table Body
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 5,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: ListView.builder(
              itemCount: _checkinStatus.length,
              itemBuilder: (context, index) {
                final checkinStatus = _checkinStatus[index];
                final checkin = checkinStatus['checkin'] ?? 'N/A';
                final checkout = checkinStatus['checkout'] ?? 'N/A';
                final hours = checkinStatus['hours'] ?? '--';

                // Alternate row colors for better readability
                final backgroundColor = index % 2 == 0 ? Colors.white : Colors.grey[50];

                return Container(
                  decoration: BoxDecoration(
                    color: backgroundColor,
                    border: Border(
                      bottom: BorderSide(color: Colors.grey[300]!),
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 4,
                          child: Text(
                            checkinStatus['dsName'] ?? 'Unknown',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: 14,
                              color: Colors.black87,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Container(
                            alignment: Alignment.center,
                            child: checkin == 'N/A'
                              ? _buildStatusChip(
                                  text: 'N/A',
                                  color: Colors.red[100]!,
                                  textColor: Colors.red[800]!,
                                )
                              : Text(
                                  checkin,
                                  style: TextStyle(
                                    color: Colors.green[800],
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Container(
                            alignment: Alignment.center,
                            child: checkout == 'N/A'
                              ? _buildStatusChip(
                                  text: 'N/A',
                                  color: Colors.orange[100]!,
                                  textColor: Colors.orange[800]!,
                                )
                              : Text(
                                  checkout,
                                  style: TextStyle(
                                    color: Colors.blue[800],
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Container(
                            alignment: Alignment.center,
                            padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: hours != '--:--'
                              ? BoxDecoration(
                                  color: Theme.of(context).primaryColor.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(12),
                                )
                              : null,
                            child: Text(
                              hours,
                              style: TextStyle(
                                fontWeight: hours != '--:--' ? FontWeight.bold : FontWeight.normal,
                                fontSize: 14,
                                color: hours != '--:--'
                                  ? Theme.of(context).primaryColor
                                  : Colors.grey[600],
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBillCutSummaryView() {
    // Check if data is still loading or if there's truly no data
    if (_billCutSummary.isEmpty) {
      // If we've already attempted to load data, show "No data" message
      if (_hasAttemptedLoadBillCut) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.receipt_long,
                size: 48,
                color: Colors.grey[400],
              ),
              SizedBox(height: 16),
              Text(
                'No data available',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[700],
                ),
              ),
              SizedBox(height: 8),
              Text(
                'There is no bill cut data for today',
                style: TextStyle(
                  color: Colors.grey[600],
                ),
              ),
              SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () {
                  setState(() {
                    _hasAttemptedLoadBillCut = false; // Reset the flag
                  });
                  _fetchBillCutSummary(); // Retry loading data
                },
                icon: Icon(Icons.refresh),
                label: Text('Refresh'),
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
              ),
            ],
          ),
        );
      }

      // Mark that we've attempted to load data
      Future.delayed(Duration(seconds: 3), () {
        if (mounted && _billCutSummary.isEmpty) {
          setState(() {
            _hasAttemptedLoadBillCut = true;
          });
        }
      });

      // Show loading indicator for the first attempt
      return Center(child: CircularProgressIndicator());
    }

    return Column(
      children: [
        // Header with date
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Bill Cut Summary',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Theme.of(context).primaryColor.withOpacity(0.2),
                ),
              ),
              child: Text(
                _formatCurrentDate(),
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 16),

        // Table Header with improved styling
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(8),
              topRight: Radius.circular(8),
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            child: Row(
              children: [
                Expanded(
                  flex: 4,
                  child: Text(
                    'Salesman',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Text(
                    'Number of Bill Cuts',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
        ),

        // Table Body
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 5,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: ListView.builder(
              itemCount: _billCutSummary.length,
              itemBuilder: (context, index) {
                final billCut = _billCutSummary[index];
                final billCount = billCut['billCount'] ?? '0';

                // Alternate row colors for better readability
                final backgroundColor = index % 2 == 0 ? Colors.white : Colors.grey[50];

                return Container(
                  decoration: BoxDecoration(
                    color: backgroundColor,
                    border: Border(
                      bottom: BorderSide(color: Colors.grey[300]!),
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 4,
                          child: Text(
                            billCut['dsName'] ?? 'Unknown',
                            style: TextStyle(fontWeight: FontWeight.w500, fontSize: 14),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Container(
                            alignment: Alignment.center,
                            child: int.parse(billCount) > 0
                              ? Container(
                                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                                  decoration: BoxDecoration(
                                    color: Colors.green[100],
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: Text(
                                    billCount,
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: Colors.green[800],
                                      fontSize: 14,
                                    ),
                                  ),
                                )
                              : Text(
                                  '0',
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: 14,
                                  ),
                                ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}