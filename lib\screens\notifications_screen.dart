import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';

class NotificationsScreen extends StatefulWidget {
  @override
  _NotificationsScreenState createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  bool _isLoading = true;
  List<Map<String, dynamic>> _notifications = [];
  String? _wdId;

  @override
  void initState() {
    super.initState();
    _getWdId().then((_) {
      _fetchNotifications();
    });
  }

  Future<void> _getWdId() async {
    try {
      User? user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        print('Getting WD ID for user: ${user.uid}');

        // First try to find the user document by authUid field
        QuerySnapshot userQuery = await FirebaseFirestore.instance
            .collection('wdUsers')
            .where('authUid', isEqualTo: user.uid)
            .limit(1)
            .get();

        // If no document found with authUid, try to find by phone number
        if (userQuery.docs.isEmpty) {
          // Extract phone number from Firebase user
          String? phoneNumber = user.phoneNumber;
          if (phoneNumber != null && phoneNumber.startsWith('+91')) {
            // Remove the +91 prefix to match our stored format
            String mobileNumber = phoneNumber.substring(3);

            userQuery = await FirebaseFirestore.instance
                .collection('wdUsers')
                .where('mobileNumber', isEqualTo: mobileNumber)
                .limit(1)
                .get();
          }
        }

        if (userQuery.docs.isEmpty) {
          print('User document does not exist in wdUsers collection');
          return;
        }

        // Get the first matching document
        DocumentSnapshot userDoc = userQuery.docs.first;

        // Update the authUid field if it's not set
        if (userDoc['authUid'] == null) {
          await FirebaseFirestore.instance
              .collection('wdUsers')
              .doc(userDoc.id)
              .update({'authUid': user.uid});
        }

        setState(() {
          _wdId = userDoc['wdId'];
        });
        print('Got WD ID: $_wdId');
      }
    } catch (e) {
      print('Error getting WD ID: $e');
    }
  }

  Future<void> _fetchNotifications() async {
    if (_wdId == null) {
      setState(() {
        _isLoading = false;
      });
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Create some test notifications if none exist
      await _ensureTestNotificationsExist();

      // Fetch notifications
      QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection('wd_notifications')
          .where('wdId', isEqualTo: _wdId)
          .get();

      print('Found ${snapshot.docs.length} notifications');

      List<Map<String, dynamic>> notifications = [];
      for (var doc in snapshot.docs) {
        Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
        notifications.add({
          'id': doc.id,
          'title': data['title'] ?? 'Notification',
          'message': data['message'] ?? '',
          'timestamp': data['timestamp'] ?? Timestamp.now(),
          'isRead': data['isRead'] ?? false,
          'type': data['type'] ?? 'general',
        });
      }

      // Sort by timestamp (newest first)
      notifications.sort((a, b) {
        Timestamp aTime = a['timestamp'] as Timestamp;
        Timestamp bTime = b['timestamp'] as Timestamp;
        return bTime.compareTo(aTime);
      });

      setState(() {
        _notifications = notifications;
        _isLoading = false;
      });

      // Mark all as read
      _markAllAsRead();
    } catch (e) {
      print('Error fetching notifications: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _ensureTestNotificationsExist() async {
    try {
      if (_wdId == null) return;

      // Check if notifications exist
      QuerySnapshot existingNotifications = await FirebaseFirestore.instance
          .collection('wd_notifications')
          .where('wdId', isEqualTo: _wdId)
          .limit(1)
          .get();

      // If no notifications exist, create some test ones
      if (existingNotifications.docs.isEmpty) {
        print('Creating test notifications');

        // Create a batch for multiple writes
        WriteBatch batch = FirebaseFirestore.instance.batch();

        // Add a welcome notification
        DocumentReference welcomeRef = FirebaseFirestore.instance.collection('wd_notifications').doc();
        batch.set(welcomeRef, {
          'title': 'Welcome to Distributor',
          'message': 'Thank you for using the Distributor app. This is your notifications center where you\'ll receive important updates.',
          'timestamp': Timestamp.now(),
          'wdId': _wdId,
          'isRead': false,
          'type': 'welcome',
        });

        // Add a feature notification
        DocumentReference featureRef = FirebaseFirestore.instance.collection('wd_notifications').doc();
        batch.set(featureRef, {
          'title': 'New Features Available',
          'message': 'Check out the new sales register and reporting features in the app!',
          'timestamp': Timestamp.fromDate(DateTime.now().subtract(Duration(hours: 2))),
          'wdId': _wdId,
          'isRead': false,
          'type': 'feature',
        });

        // Add a reminder notification
        DocumentReference reminderRef = FirebaseFirestore.instance.collection('wd_notifications').doc();
        batch.set(reminderRef, {
          'title': 'Daily Reminder',
          'message': 'Don\'t forget to check your sales reports for today.',
          'timestamp': Timestamp.fromDate(DateTime.now().subtract(Duration(days: 1))),
          'wdId': _wdId,
          'isRead': false,
          'type': 'reminder',
        });

        // Commit the batch
        await batch.commit();
        print('Test notifications created');
      }
    } catch (e) {
      print('Error ensuring test notifications: $e');
    }
  }

  Future<void> _markAllAsRead() async {
    try {
      if (_wdId == null) return;

      // Get all unread notifications
      QuerySnapshot unreadNotifications = await FirebaseFirestore.instance
          .collection('wd_notifications')
          .where('wdId', isEqualTo: _wdId)
          .where('isRead', isEqualTo: false)
          .get();

      print('Marking notifications as read: ${unreadNotifications.docs.length} notifications');

      if (unreadNotifications.docs.isEmpty) {
        print('No unread notifications to update');
        return;
      }

      // Create a batch for multiple updates
      WriteBatch batch = FirebaseFirestore.instance.batch();

      // Mark each notification as read
      for (var doc in unreadNotifications.docs) {
        print('Marking notification as read: ${doc.id}');
        batch.update(doc.reference, {'isRead': true});
      }

      // Commit the batch
      await batch.commit();
      print('Successfully marked ${unreadNotifications.docs.length} notifications as read');

      // Update local state
      setState(() {
        for (var notification in _notifications) {
          notification['isRead'] = true;
        }
      });
    } catch (e) {
      print('Error marking notifications as read: $e');
    }
  }

  String _formatTimestamp(Timestamp timestamp) {
    DateTime dateTime = timestamp.toDate();
    DateTime now = DateTime.now();

    // If it's today, show only the time
    if (dateTime.year == now.year &&
        dateTime.month == now.month &&
        dateTime.day == now.day) {
      return 'Today at ${DateFormat('h:mm a').format(dateTime)}';
    }

    // If it's yesterday, show "Yesterday"
    DateTime yesterday = now.subtract(Duration(days: 1));
    if (dateTime.year == yesterday.year &&
        dateTime.month == yesterday.month &&
        dateTime.day == yesterday.day) {
      return 'Yesterday at ${DateFormat('h:mm a').format(dateTime)}';
    }

    // Otherwise, show the full date
    return DateFormat('MMM d, yyyy h:mm a').format(dateTime);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Notifications'),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _fetchNotifications,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : _notifications.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.notifications_off,
                        size: 64,
                        color: Colors.grey,
                      ),
                      SizedBox(height: 16),
                      Text(
                        'No notifications yet',
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  itemCount: _notifications.length,
                  itemBuilder: (context, index) {
                    final notification = _notifications[index];
                    final bool isRead = notification['isRead'];
                    final IconData typeIcon = _getNotificationIcon(notification['type']);

                    return Card(
                      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      elevation: isRead ? 1 : 3,
                      color: isRead ? null : Colors.blue.shade50,
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: isRead ? Colors.grey.shade200 : Colors.blue.shade100,
                          child: Icon(
                            typeIcon,
                            color: isRead ? Colors.grey : Colors.blue,
                          ),
                        ),
                        title: Text(
                          notification['title'],
                          style: TextStyle(
                            fontWeight: isRead ? FontWeight.normal : FontWeight.bold,
                          ),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(height: 4),
                            Text(
                              notification['message'],
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            SizedBox(height: 4),
                            Text(
                              _formatTimestamp(notification['timestamp']),
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey,
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                          ],
                        ),
                        isThreeLine: true,
                        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        onTap: () {
                          // Show dialog with full notification details
                          _showNotificationDetails(notification);
                        },
                      ),
                    );
                  },
                ),
    );
  }

  // Show notification details in a dialog
  void _showNotificationDetails(Map<String, dynamic> notification) {
    final IconData typeIcon = _getNotificationIcon(notification['type']);
    final bool isRead = notification['isRead'];

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              CircleAvatar(
                backgroundColor: isRead ? Colors.grey.shade200 : Colors.blue.shade100,
                child: Icon(
                  typeIcon,
                  color: isRead ? Colors.grey : Colors.blue,
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: Text(
                  notification['title'],
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  notification['message'],
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(height: 16),
                Text(
                  _formatTimestamp(notification['timestamp']),
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('Close'),
            ),
          ],
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          contentPadding: EdgeInsets.fromLTRB(24, 20, 24, 0),
          actionsPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        );
      },
    );
  }

  IconData _getNotificationIcon(String type) {
    switch (type) {
      case 'sales':
        return Icons.point_of_sale;
      case 'checkin':
        return Icons.login;
      case 'checkout':
        return Icons.logout;
      case 'beat':
        return Icons.location_on;
      case 'ds':
        return Icons.person;
      case 'test':
        return Icons.new_releases;
      default:
        return Icons.notifications;
    }
  }
}
