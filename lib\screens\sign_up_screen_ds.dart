import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class SignUpScreen extends StatefulWidget {
  @override
  _SignUpScreenState createState() => _SignUpScreenState();
}

class _SignUpScreenState extends State<SignUpScreen> {
  bool _isSubmitting = false; // Flag to prevent multiple submission attempts
  final _nameController = TextEditingController();
  final _mobileNumberController = TextEditingController();
  final _pincodeController = TextEditingController();
  // Controllers for fields that will be auto-populated.
  final _regionController = TextEditingController();
  final _stateController = TextEditingController();
  final _cityController = TextEditingController();
  final _edCodeController = TextEditingController();
  String? _selectedState;
  String? _selectedRegion;
  String? _edCode; // This is auto-populated based on the current user
  bool _isPincodeValid = false; // Flag to track if pincode is valid

  // Cache for pincode lookup results to avoid repeated API calls
  final Map<String, Map<String, String>> _pincodeCache = {};

  final _formKey = GlobalKey<FormState>();

  // Map of states to their respective regions
  final Map<String, String> _stateRegions = {
    'Andhra Pradesh': 'South',
    'Arunachal Pradesh': 'North East',
    'Assam': 'North East',
    'Bihar': 'East',
    'Chhattisgarh': 'Central',
    'Delhi': 'North',
    'Goa': 'West',
    'Gujarat': 'West',
    'Haryana': 'North',
    'Himachal Pradesh': 'North',
    'Jharkhand': 'East',
    'Karnataka': 'South',
    'Kerala': 'South',
    'Madhya Pradesh': 'Central',
    'Maharashtra': 'West',
    'Manipur': 'North East',
    'Meghalaya': 'North East',
    'Mizoram': 'North East',
    'Nagaland': 'North East',
    'Odisha': 'East',
    'Punjab': 'North',
    'Rajasthan': 'West',
    'Sikkim': 'North East',
    'Tamil Nadu': 'South',
    'Telangana': 'South',
    'Tripura': 'North East',
    'Uttar Pradesh': 'North',
    'Uttarakhand': 'North',
    'West Bengal': 'East',
  };

  // Map of pincode prefixes to states
  final Map<String, String> _pincodePrefixToState = {
    '11': 'Delhi',
    '12': 'Haryana',
    '13': 'Punjab',
    '14': 'Punjab',
    '15': 'Himachal Pradesh',
    '16': 'Himachal Pradesh',
    '17': 'Himachal Pradesh',
    '18': 'Jammu and Kashmir',
    '19': 'Jammu and Kashmir',
    '20': 'Uttar Pradesh',
    '21': 'Uttar Pradesh',
    '22': 'Uttar Pradesh',
    '23': 'Uttar Pradesh',
    '24': 'Uttar Pradesh',
    '25': 'Uttar Pradesh',
    '26': 'Uttar Pradesh',
    '27': 'Uttar Pradesh',
    '28': 'Uttar Pradesh',
    '30': 'Rajasthan',
    '31': 'Rajasthan',
    '32': 'Rajasthan',
    '33': 'Rajasthan',
    '34': 'Rajasthan',
    '36': 'Gujarat',
    '37': 'Gujarat',
    '38': 'Gujarat',
    '39': 'Gujarat',
    '40': 'Maharashtra',
    '41': 'Maharashtra',
    '42': 'Maharashtra',
    '43': 'Maharashtra',
    '44': 'Maharashtra',
    '45': 'Maharashtra',
    '50': 'Telangana',
    '51': 'Andhra Pradesh',
    '52': 'Andhra Pradesh',
    '53': 'Karnataka',
    '56': 'Karnataka',
    '57': 'Karnataka',
    '60': 'Tamil Nadu',
    '61': 'Tamil Nadu',
    '62': 'Tamil Nadu',
    '63': 'Tamil Nadu',
    '64': 'Tamil Nadu',
    '67': 'Kerala',
    '68': 'Kerala',
    '69': 'Kerala',
    '70': 'West Bengal',
    '71': 'West Bengal',
    '72': 'West Bengal',
    '73': 'West Bengal',
    '74': 'Odisha',
    '75': 'Odisha',
    '76': 'Odisha',
    '77': 'Odisha',
    '78': 'Assam',
    '79': 'Assam',
    '80': 'Bihar',
    '81': 'Bihar',
    '82': 'Bihar',
    '83': 'Jharkhand',
    '84': 'Jharkhand',
    '85': 'Chhattisgarh',
    '86': 'Chhattisgarh',
    '49': 'Madhya Pradesh',
    '45': 'Madhya Pradesh',
    '46': 'Madhya Pradesh',
    '47': 'Madhya Pradesh',
    '48': 'Madhya Pradesh',
    '79': 'Arunachal Pradesh',
    '79': 'Nagaland',
    '79': 'Manipur',
    '79': 'Mizoram',
    '79': 'Tripura',
    '79': 'Meghalaya',
    '17': 'Uttarakhand',
    '24': 'Uttarakhand',
    '18': 'Sikkim',
    '40': 'Goa',
  };

  @override
  void initState() {
    super.initState();
    _fetchEdCode();

    // Add listener to pincode field
    _pincodeController.addListener(_onPincodeChanged);
  }

  @override
  void dispose() {
    // Remove listener when widget is disposed
    _pincodeController.removeListener(_onPincodeChanged);
    super.dispose();
  }

  // Debounce timer for API calls
  DateTime? _lastPincodeChangeTime;

  void _onPincodeChanged() async {
    final pincode = _pincodeController.text.trim();

    // Reset fields if pincode is not 6 digits
    if (pincode.length != 6) {
      setState(() {
        _isPincodeValid = false;
        _stateController.text = '';
        _regionController.text = '';
        _cityController.text = '';
      });
      return;
    }

    // Set a debounce of 500ms to avoid too many API calls while typing
    _lastPincodeChangeTime = DateTime.now();
    await Future.delayed(Duration(milliseconds: 500));
    if (_lastPincodeChangeTime != null &&
        DateTime.now().difference(_lastPincodeChangeTime!).inMilliseconds < 500) {
      return; // Another change was made, skip this one
    }

    // Show loading state
    setState(() {
      _stateController.text = 'Loading...';
      _cityController.text = 'Loading...';
      _regionController.text = 'Loading...';
    });

    try {
      // Check if we have this pincode in cache
      if (_pincodeCache.containsKey(pincode)) {
        _updateFieldsFromPincodeData(_pincodeCache[pincode]!);
        return;
      }

      // Call the API to get pincode details
      final response = await http.get(
        Uri.parse('https://api.postalpincode.in/pincode/$pincode')
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);

        if (data.isNotEmpty && data[0]['Status'] == 'Success') {
          final List<dynamic> postOffices = data[0]['PostOffice'];

          if (postOffices.isNotEmpty) {
            final postOffice = postOffices[0];

            // Extract relevant information
            final Map<String, String> pincodeData = {
              'city': postOffice['Block'] ?? postOffice['Name'] ?? '',
              'state': postOffice['State'] ?? '',
              'district': postOffice['District'] ?? '',
            };

            // Cache the result
            _pincodeCache[pincode] = pincodeData;

            // Update the UI
            _updateFieldsFromPincodeData(pincodeData);
            return;
          }
        }

        // If we get here, the API didn't return valid data
        setState(() {
          _isPincodeValid = false;
          _stateController.text = 'Invalid pincode';
          _regionController.text = '';
          _cityController.text = '';
        });
      } else {
        // API call failed
        setState(() {
          _isPincodeValid = false;
          _stateController.text = 'Error fetching pincode data';
          _regionController.text = '';
          _cityController.text = '';
        });
      }
    } catch (e) {
      print('Error fetching pincode data: $e');
      setState(() {
        _isPincodeValid = false;
        _stateController.text = 'Error: $e';
        _regionController.text = '';
        _cityController.text = '';
      });
    }
  }

  void _updateFieldsFromPincodeData(Map<String, String> pincodeData) {
    setState(() {
      final state = pincodeData['state'] ?? '';
      final city = pincodeData['city'] ?? '';
      final district = pincodeData['district'] ?? '';

      _isPincodeValid = true;
      _stateController.text = state;
      _selectedState = state;

      // Set city (prefer Block/Name, fallback to District)
      _cityController.text = city.isNotEmpty ? city : district;

      // Set region based on state if available, otherwise use district
      final region = _stateRegions[state];
      if (region != null) {
        _selectedRegion = region;
        _regionController.text = region;
      } else {
        _regionController.text = district;
      }
    });
  }

  /// Fetch the current user's ED Code from Firestore and auto-populate it.
  Future<void> _fetchEdCode() async {
    try {
      User? user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        // First try to find the user document by authUid field
        QuerySnapshot userQuery = await FirebaseFirestore.instance
            .collection('wdUsers')
            .where('authUid', isEqualTo: user.uid)
            .limit(1)
            .get();

        // If no document found with authUid, try to find by phone number
        if (userQuery.docs.isEmpty) {
          // Extract phone number from Firebase user
          String? phoneNumber = user.phoneNumber;
          if (phoneNumber != null && phoneNumber.startsWith('+91')) {
            // Remove the +91 prefix to match our stored format
            String mobileNumber = phoneNumber.substring(3);

            userQuery = await FirebaseFirestore.instance
                .collection('wdUsers')
                .where('mobileNumber', isEqualTo: mobileNumber)
                .limit(1)
                .get();
          }
        }

        if (userQuery.docs.isEmpty) {
          print('User document does not exist in wdUsers collection');
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('User data not found. Please log out and log in again.'),
          ));
          // Navigate back to dashboard or login screen
          Navigator.pop(context);
          return;
        }

        // Get the first matching document
        DocumentSnapshot userDoc = userQuery.docs.first;

        // Update the authUid field if it's not set
        if (userDoc['authUid'] == null) {
          await FirebaseFirestore.instance
              .collection('wdUsers')
              .doc(userDoc.id)
              .update({'authUid': user.uid});
        }

        // Assuming the field 'wdId' contains the ED Code.
        String fetchedEdCode = userDoc.get('wdId') ?? '';
        setState(() {
          _edCodeController.text = fetchedEdCode; // Assuming the field 'wdId' contains the ED Code.
        });
      }
    } catch (e) {
      print('Error fetching ED Code: $e');
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Error loading data. Please try again later.'),
      ));
    }
  }

  Future<String> _generateDsId() async {
    final QuerySnapshot querySnapshot = await FirebaseFirestore.instance
        .collection('users')
        .orderBy('dsId', descending: true)
        .limit(1)
        .get();

    if (querySnapshot.docs.isNotEmpty) {
      String lastDsId = querySnapshot.docs.first['dsId'];
      int lastIdNumber = int.parse(lastDsId.split('-').last);
      int newIdNumber = lastIdNumber + 1;
      return 'QK-DS-${newIdNumber.toString().padLeft(6, '0')}';
    } else {
      return 'QK-DS-000001';
    }
  }

  void _signUp() async {
    // Prevent multiple submission attempts
    if (_isSubmitting) return;

    if (_formKey.currentState!.validate()) {
      setState(() {
        _isSubmitting = true; // Set flag to prevent multiple attempts
      });

      final name = _nameController.text;
      final mobileNumber = _mobileNumberController.text;
      final pincode = _pincodeController.text;
      final state = _stateController.text;
      final city = _cityController.text;
      final region = _regionController.text;
      final edCode = _edCodeController.text;
      final email = '$<EMAIL>'; // Create email from mobile number
      final password = mobileNumber; // Initial password is the mobile number

      if (edCode.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('ED Code is missing. Please log out and log in again.')),
        );
        setState(() {
          _isSubmitting = false;
        });
        return;
      }

      if (!_isPincodeValid) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Please enter a valid pincode')),
        );
        setState(() {
          _isSubmitting = false;
        });
        return;
      }

      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => Center(
          child: CircularProgressIndicator(),
        ),
      );

      try {
        // Get the current user to ensure we're still logged in
        User? currentUser = FirebaseAuth.instance.currentUser;
        if (currentUser == null) {
          Navigator.pop(context); // Close loading indicator
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('You are not logged in. Please log in again.')),
          );
          return;
        }

        // First try to find the user document by authUid field
        QuerySnapshot userQuery = await FirebaseFirestore.instance
            .collection('wdUsers')
            .where('authUid', isEqualTo: currentUser.uid)
            .limit(1)
            .get();

        // If no document found with authUid, try to find by phone number
        if (userQuery.docs.isEmpty) {
          // Extract phone number from Firebase user
          String? phoneNumber = currentUser.phoneNumber;
          if (phoneNumber != null && phoneNumber.startsWith('+91')) {
            // Remove the +91 prefix to match our stored format
            String mobileNumber = phoneNumber.substring(3);

            userQuery = await FirebaseFirestore.instance
                .collection('wdUsers')
                .where('mobileNumber', isEqualTo: mobileNumber)
                .limit(1)
                .get();
          }
        }

        if (userQuery.docs.isEmpty) {
          Navigator.pop(context); // Close loading indicator
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Your user data is missing. Please log out and log in again.')),
          );
          return;
        }

        // Get the first matching document
        DocumentSnapshot userDoc = userQuery.docs.first;

        // IMPORTANT: We need to check if the mobile number exists in Firestore collections

        // 1. First, check if the user exists in the users collection (DS users)
        QuerySnapshot existingUsers = await FirebaseFirestore.instance
            .collection('users')
            .where('mobileNumber', isEqualTo: mobileNumber)
            .get();

        if (existingUsers.docs.isNotEmpty) {
          Navigator.pop(context); // Close loading indicator
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('A Salesman with this mobile number already exists in the database.')),
          );
          return;
        }

        // 2. Check if the user exists in the wdUsers collection (WD users)
        QuerySnapshot wdUsers = await FirebaseFirestore.instance
            .collection('wdUsers')
            .where('mobileNumber', isEqualTo: mobileNumber)
            .get();

        if (wdUsers.docs.isNotEmpty) {
          Navigator.pop(context); // Close loading indicator
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('A user with this mobile number already exists.')),
          );
          return;
        }

        // Display a message to the user that we're checking for existing accounts
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Checking for existing accounts...'),
            duration: Duration(seconds: 1),
          ),
        );

        // 3. Check if the user exists in Firebase Authentication
        // Create email from mobile number (this is how Firebase Auth stores users in this app)
        final email = '$<EMAIL>';

        try {
          // Use fetchSignInMethodsForEmail to check if the email exists in Firebase Auth
          final List<String> signInMethods = await FirebaseAuth.instance.fetchSignInMethodsForEmail(email);

          // If the list is not empty, it means the email is already registered
          if (signInMethods.isNotEmpty) {
            Navigator.pop(context); // Close loading indicator
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('A user with this mobile number already exists in authentication system.')),
            );
            return;
          }
        } catch (e) {
          print('Error checking email auth: $e');
          // Continue with the process even if this check fails
        }

        // Generate DS ID
        String dsId = await _generateDsId();

        // For now, we'll just create the Firestore document without creating a Firebase Auth user
        // The DS will create their own Firebase Auth account when they sign up in their app
        // This approach keeps the current admin logged in

        // Create the DS user document in Firestore with authentication info
        await FirebaseFirestore.instance.collection('users').doc(dsId).set({
          'name': name,
          'mobileNumber': mobileNumber,
          'pincode': pincode,
          'city': _cityController.text,
          'dsId': dsId,
          'wdCode': edCode, // Auto-populated ED Code
          'kyc': 'pending',
          'status': 'active', // Default status is active
          'state': state,
          'region': region,
          'passwordResetRequired': true,
          'email': email, // Store the email for reference
          'password': password, // Store the initial password
          'appleQty': 0, // Default value for Apple Qty
          'varianceQty': 0, // Default value for Variance Qty
          'strawberryQty': 0, // Default value for Strawberry Qty
          'gumMintQty': 0, // Default value for Gum Mint Qty
          'watermelonQty': 0, // Default value for Watermelon Qty
          'paanQty': 0, // Default value for Paan Qty
          'changeQty': 0, // Default value for Change Qty
          'createdAt': FieldValue.serverTimestamp(),
          'authStatus': 'pending', // Indicates that Firebase Auth account needs to be created
        });

        // Close loading indicator
        Navigator.pop(context);

        // Show a success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Salesman onboarded successfully with ID: $dsId')),
        );

        // Return to the previous screen
        Navigator.pop(context);
      } catch (e) {
        // Close loading indicator
        Navigator.pop(context);

        print('Error in sign up: $e');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to sign up: $e')),
        );
      } finally {
        // Reset the flag regardless of success or failure
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Onboard Salesman'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header
              Padding(
                padding: const EdgeInsets.only(bottom: 24.0),
                child: Text(
                  'Add New Salesman Staff',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.black),
                  textAlign: TextAlign.center,
                ),
              ),

              // Name Field
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: TextFormField(
                  controller: _nameController,
                  decoration: InputDecoration(
                    labelText: 'Name',
                    hintText: 'Enter Salesman full name',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.person),
                    filled: true,
                    fillColor: Colors.grey[100],
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter the Salesman name';
                    }
                    return null;
                  },
                ),
              ),

              // Mobile Number Field
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: TextFormField(
                  controller: _mobileNumberController,
                  decoration: InputDecoration(
                    labelText: 'Mobile Number',
                    hintText: 'Enter 10-digit mobile number',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.phone_android),
                    filled: true,
                    fillColor: Colors.grey[100],
                  ),
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(10),
                  ],
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter mobile number';
                    } else if (value.length != 10) {
                      return 'Mobile number must be exactly 10 digits';
                    }
                    return null;
                  },
                ),
              ),

              // Pincode Field
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: TextFormField(
                  controller: _pincodeController,
                  decoration: InputDecoration(
                    labelText: 'Pincode',
                    hintText: 'Enter 6-digit pincode',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.location_on),
                    filled: true,
                    fillColor: Colors.grey[100],
                    suffixIcon: _isPincodeValid
                      ? Icon(Icons.check_circle, color: Colors.green)
                      : (_pincodeController.text.length == 6
                          ? Icon(Icons.error, color: Colors.red)
                          : null),
                  ),
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(6),
                  ],
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter pincode';
                    } else if (value.length != 6) {
                      return 'Pincode must be exactly 6 digits';
                    } else if (!_isPincodeValid) {
                      return 'Invalid pincode or region not recognized';
                    }
                    return null;
                  },
                ),
              ),

              // State Field (Auto-populated based on pincode)
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: TextFormField(
                  controller: _stateController,
                  decoration: InputDecoration(
                    labelText: 'State',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.location_city),
                    filled: true,
                    fillColor: Colors.grey[200], // Darker background for disabled fields
                  ),
                  enabled: false,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'State is required';
                    }
                    return null;
                  },
                ),
              ),

              // City Field (Auto-populated based on pincode)
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: TextFormField(
                  controller: _cityController,
                  decoration: InputDecoration(
                    labelText: 'City',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.location_city),
                    filled: true,
                    fillColor: Colors.grey[200], // Darker background for disabled fields
                  ),
                  enabled: false,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'City is required';
                    }
                    return null;
                  },
                ),
              ),

              // Region (Auto-populated based on state)
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: TextFormField(
                  controller: _regionController,
                  decoration: InputDecoration(
                    labelText: 'Region',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.map),
                    filled: true,
                    fillColor: Colors.grey[200], // Darker background for disabled fields
                  ),
                  enabled: false,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Region is required';
                    }
                    return null;
                  },
                ),
              ),

              // ED Code is hidden but still validated
              Visibility(
                visible: false,
                maintainState: true,
                child: TextFormField(
                  controller: _edCodeController,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'ED Code is required';
                    }
                    return null;
                  },
                ),
              ),

              // Sign Up Button
              SizedBox(
                height: 50,
                child: ElevatedButton(
                  onPressed: _isSubmitting ? null : _signUp, // Disable button while submitting
                  style: ElevatedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                  ),
                  child: _isSubmitting
                    ? SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        'Onboard Salesman',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                ),
              ),

              SizedBox(height: 16),

              // Note about initial password
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: Text(
                  'Note: The Salesman will their mobile number and password to login.',
                  style: TextStyle(fontSize: 12, color: Colors.grey[700], fontStyle: FontStyle.italic),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}